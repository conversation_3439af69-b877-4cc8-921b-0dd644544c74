import request from '@/utils/request'

/**
 * TikTok Shop API相关接口
 */

/**
 * 联盟选品查询（V202501版本）
 * @param {Object} params 查询参数 - 与AffiliateProductSearchRequest保持一致
 * @param {Number} params.pageSize 每页数量，默认20，最大100
 * @param {String} params.cursor 分页游标，用于获取下一页数据
 * @param {String} params.sortType 排序类型：RECOMMENDED-推荐，BEST_SELLERS-销量高到低，LOW_PRICE-价格低到高，HIGH_PRICE-价格高到低，NEWLY_RELEASED-最新发布，HIGH_COMMISSIOM_RATE-佣金率高到低
 * @param {Array} params.productIds 精确商品ID列表，最多50个，如果不为空则忽略其他字段
 * @param {String} params.titleKeyword 商品名称关键词，用于模糊搜索，1-255字符
 * @param {Array} params.categoryIds 类目ID列表，最多1000个
 * @param {Number} params.commissionRateGe 佣金率最小值（基点，如1200表示12%），范围100-8000
 * @param {Number} params.commissionRateLe 佣金率最大值（基点，如1200表示12%），范围100-8000
 * @param {Number} params.priceGe 价格最小值（整数），单位为创作者营销国家的本地货币
 * @param {Number} params.priceLe 价格最大值（整数），单位为创作者营销国家的本地货币
 * @param {Number} params.shopRatingGe 店铺评分最小值（基点，如35表示3.5分），范围0-50
 * @param {Number} params.shopRatingLe 店铺评分最大值（基点，如35表示3.5分），范围0-50
 * @param {Number} params.soldQuantityGe 销量最小值，大于等于0
 * @param {Number} params.soldQuantityLe 销量最大值，大于等于0，0或空表示无限制
 * @param {Array} params.poolIds 产品池ID列表，无title_keyword时仅支持1个

 */
export function searchAffiliateProducts(params) {
  return request({
    url: '/admin/affiliate/products/search',
    method: 'post',
    data: params
  })
}
