<template>
  <div class="divBox relative">
    <!-- 搜索表单 -->
    <el-card class="box-card">
      <el-form :model="searchForm" :rules="searchRules" ref="searchFormRef" inline size="small">
        <el-form-item label="关键词：">
          <el-input
            v-model="searchForm.titleKeyword"
            placeholder="请输入商品关键词"
            clearable
            style="width: 200px;"
          ></el-input>
        </el-form-item>

        <el-form-item label="价格范围：">
          <el-input
            v-model="searchForm.priceMin"
            placeholder="最低价格"
            style="width: 120px;"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.priceMax"
            placeholder="最高价格"
            style="width: 120px;"
          ></el-input>
        </el-form-item>

        <el-form-item label="佣金率范围：">
          <el-input
            v-model="searchForm.commissionMin"
            placeholder="最低佣金率(%)"
            style="width: 120px;"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.commissionMax"
            placeholder="最高佣金率(%)"
            style="width: 120px;"
          ></el-input>
        </el-form-item>

        <el-form-item label="销量范围：">
          <el-input
            v-model="searchForm.salesMin"
            placeholder="最低销量"
            style="width: 120px;"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.salesMax"
            placeholder="最高销量"
            style="width: 120px;"
          ></el-input>
        </el-form-item>

        <el-form-item label="店铺评分：">
          <el-input
            v-model="searchForm.shopRatingMin"
            placeholder="最低评分"
            style="width: 120px;"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.shopRatingMax"
            placeholder="最高评分"
            style="width: 120px;"
          ></el-input>
        </el-form-item>

        <el-form-item label="排序：">
          <el-select v-model="searchForm.sortType" style="width: 200px;">
            <el-option label="推荐" value="RECOMMENDED"></el-option>
            <el-option label="销量高到低" value="BEST_SELLERS"></el-option>
            <el-option label="价格低到高" value="LOW_PRICE"></el-option>
            <el-option label="价格高到低" value="HIGH_PRICE"></el-option>
            <el-option label="最新发布" value="NEWLY_RELEASED"></el-option>
            <el-option label="佣金率高到低" value="HIGH_COMMISSIOM_RATE"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 产品列表 -->
    <el-card class="box-card" style="margin-top: 12px;">
      <div slot="header" class="clearfix">
        <span>联盟选品列表</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="handleRefresh"
        >刷新</el-button>
      </div>

      <!-- 未搜索时的提示 -->
      <div v-if="!hasSearched && tableData.length === 0" class="empty-tip">
        <el-empty description="点击查询按钮开始搜索商品"></el-empty>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
        v-show="hasSearched"
      >
        <el-table-column type="index" label="序号" width="60"></el-table-column>

        <el-table-column label="商品图片" width="100">
          <template slot-scope="scope">
            <el-image
              :src="scope.row.mainImageUrl"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px;"
              :preview-src-list="[scope.row.mainImageUrl]"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>

        <el-table-column label="商品标题" min-width="200">
          <template slot-scope="scope">
            <div style="word-break: break-word;">
              {{ scope.row.title }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="店铺" width="120">
          <template slot-scope="scope">
            {{ scope.row.shop ? scope.row.shop.name : '-' }}
          </template>
        </el-table-column>

        <el-table-column label="原价" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.originalPrice">
              {{ formatPrice(scope.row.originalPrice) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="售价" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.salesPrice">
              {{ formatPrice(scope.row.salesPrice) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="佣金率" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.commission">
              {{ formatCommissionRate(scope.row.commission.rate) }}%
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="佣金金额" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.commission">
              {{ scope.row.commission.amount }} {{ scope.row.commission.currency }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="销量" width="80">
          <template slot-scope="scope">
            {{ scope.row.unitsSold || 0 }}
          </template>
        </el-table-column>

        <el-table-column label="库存状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.hasInventory ? 'success' : 'danger'" size="mini">
              {{ scope.row.hasInventory ? '有库存' : '无库存' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="评分" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.review && scope.row.review.overallScore">
              {{ parseFloat(scope.row.review.overallScore).toFixed(1) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="评论数" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.review && scope.row.review.count">
              {{ scope.row.review.count }}
            </span>
            <span v-else>0</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="hasSearched" class="pagination-container" style="margin-top: 20px; text-align: center;">
        <el-button
          :disabled="!hasPrevPage"
          @click="handlePrevPage"
          size="small"
        >上一页</el-button>
        <el-button
          :disabled="!hasNextPage"
          @click="handleNextPage"
          size="small"
        >下一页</el-button>
        <span style="margin-left: 20px;">
          每页显示：
          <el-select v-model="searchForm.pageSize" size="mini" style="width: 80px;" @change="handleSearch">
            <el-option label="10" :value="10"></el-option>
            <el-option label="20" :value="20"></el-option>
            <el-option label="50" :value="50"></el-option>
          </el-select>
        </span>
        <span style="margin-left: 20px;">
          共 {{ totalCount }} 个商品
        </span>
      </div>
    </el-card>
  </div>
</template>

<script>
import { searchAffiliateProducts } from '@/api/tiktok'

export default {
  name: 'AffiliateProducts',
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        titleKeyword: '',
        priceMin: '',
        priceMax: '',
        commissionMin: '',
        commissionMax: '',
        salesMin: '',
        salesMax: '',
        shopRatingMin: '',
        shopRatingMax: '',
        sortType: 'RECOMMENDED',
        pageSize: 20
      },
      searchRules: {
        // 移除关键词必填验证
      },
      currentCursor: '',
      nextPageToken: '',
      prevPageTokens: [], // 存储前面页面的token，用于返回上一页
      totalCount: 0,
      hasNextPage: false,
      hasPrevPage: false,
      hasSearched: false // 标记是否已经搜索过
    }
  },
  mounted() {
    // 页面加载时不自动搜索，等待用户点击查询按钮
  },
  methods: {
    // 搜索
    handleSearch() {
      this.currentCursor = ''
      this.nextPageToken = ''
      this.prevPageTokens = []
      this.hasPrevPage = false
      this.hasSearched = true
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        titleKeyword: '',
        priceMin: '',
        priceMax: '',
        commissionMin: '',
        commissionMax: '',
        salesMin: '',
        salesMax: '',
        shopRatingMin: '',
        shopRatingMax: '',
        sortType: 'RECOMMENDED',
        pageSize: 20
      }
      // 重置表单（无需验证状态重置）
      // 清空表格数据
      this.tableData = []
      this.hasSearched = false
      this.totalCount = 0
      this.hasNextPage = false
      this.hasPrevPage = false
      this.currentCursor = ''
      this.nextPageToken = ''
      this.prevPageTokens = []
    },

    // 刷新
    handleRefresh() {
      if (this.hasSearched) {
        this.loadData()
      } else {
        this.$message.warning('请先点击查询按钮')
      }
    },

    // 下一页
    handleNextPage() {
      if (this.hasNextPage && this.nextPageToken) {
        this.prevPageTokens.push(this.currentCursor)
        this.currentCursor = this.nextPageToken
        this.hasPrevPage = true
        this.loadData()
      }
    },

    // 上一页
    handlePrevPage() {
      if (this.hasPrevPage && this.prevPageTokens.length > 0) {
        this.currentCursor = this.prevPageTokens.pop()
        if (this.prevPageTokens.length === 0) {
          this.hasPrevPage = false
        }
        this.loadData()
      }
    },

    // 加载数据
    loadData() {
      this.loading = true

      // 构建请求参数 - 与AffiliateProductSearchRequest保持完全一致
      const params = {
        // 基础分页和排序参数
        pageSize: this.searchForm.pageSize,
        cursor: this.currentCursor,
        sortType: this.searchForm.sortType,

        // 搜索过滤参数（按V202501 API字段名）
        productIds: null, // 精确商品ID列表，暂未在前端表单中实现
        titleKeyword: this.searchForm.titleKeyword,
        categoryIds: null, // 类目ID列表，暂未在前端表单中实现
        commissionRateGe: this.searchForm.commissionMin ? parseInt(this.searchForm.commissionMin * 100) : null,
        commissionRateLe: this.searchForm.commissionMax ? parseInt(this.searchForm.commissionMax * 100) : null,
        priceGe: this.searchForm.priceMin ? parseInt(this.searchForm.priceMin) : null,
        priceLe: this.searchForm.priceMax ? parseInt(this.searchForm.priceMax) : null,
        shopRatingGe: this.searchForm.shopRatingMin ? parseInt(this.searchForm.shopRatingMin * 10) : null,
        shopRatingLe: this.searchForm.shopRatingMax ? parseInt(this.searchForm.shopRatingMax * 10) : null,
        soldQuantityGe: this.searchForm.salesMin ? parseInt(this.searchForm.salesMin) : null,
        soldQuantityLe: this.searchForm.salesMax ? parseInt(this.searchForm.salesMax) : null,
        poolIds: null // 产品池ID列表，暂未在前端表单中实现
      }

      searchAffiliateProducts(params)
        .then(res => {
          this.tableData = res.products || []
          this.nextPageToken = res.nextPageToken || ''
          this.totalCount = res.totalCount || 0
          this.hasNextPage = !!this.nextPageToken

          if (this.tableData.length === 0) {
            this.$message.info('未找到符合条件的商品')
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 格式化价格
    formatPrice(priceObj) {
      if (!priceObj) return '-'
      const min = priceObj.minimumAmount
      const max = priceObj.maximumAmount
      const currency = priceObj.currency

      if (min === max) {
        return `${min} ${currency}`
      } else {
        return `${min} - ${max} ${currency}`
      }
    },

    // 格式化佣金率（基点转百分比）
    formatCommissionRate(rate) {
      if (!rate) return '0'
      return (rate / 100).toFixed(2)
    }
  }
}
</script>

<style scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.empty-tip {
  padding: 40px 0;
  text-align: center;
}
</style>
