package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

import java.util.List;

/**
 * 联盟选品搜索请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AffiliateProductSearchRequest", description = "联盟选品搜索请求对象")
public class AffiliateProductSearchRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "每页数量，默认20，最大100")
    @Min(value = 1, message = "每页数量不能小于1")
    @Max(value = 100, message = "每页数量不能大于100")
    private Long pageSize = 20L;

    @ApiModelProperty(value = "分页游标，用于获取下一页数据")
    private String cursor;

    @ApiModelProperty(value = "排序类型：RECOMMENDED-推荐，BEST_SELLERS-销量，LOW_PRICE-价格低到高，HIGH_PRICE-价格高到低，NEWLY_RELEASED-最新发布，HIGH_COMMISSIOM_RATE-佣金率高到低")
    private String sortType = "RECOMMENDED";

    @ApiModelProperty(value = "精确商品ID列表，最多50个，如果不为空则忽略其他字段")
    private List<String> productIds;

    @ApiModelProperty(value = "商品名称关键词，用于模糊搜索，1-255字符")
    private String titleKeyword;

    @ApiModelProperty(value = "类目ID列表，最多1000个")
    private List<String> categoryIds;

    @ApiModelProperty(value = "佣金率最小值（基点，如1200表示12%），范围100-8000")
    @Min(value = 100, message = "佣金率最小值不能小于100基点")
    @Max(value = 8000, message = "佣金率最小值不能大于8000基点")
    private Integer commissionRateGe;

    @ApiModelProperty(value = "佣金率最大值（基点，如1200表示12%），范围100-8000")
    @Min(value = 100, message = "佣金率最大值不能小于100基点")
    @Max(value = 8000, message = "佣金率最大值不能大于8000基点")
    private Integer commissionRateLe;

    @ApiModelProperty(value = "价格最小值（整数），单位为创作者营销国家的本地货币")
    @Min(value = 0, message = "价格最小值不能小于0")
    private Integer priceGe;

    @ApiModelProperty(value = "价格最大值（整数），单位为创作者营销国家的本地货币")
    @Min(value = 0, message = "价格最大值不能小于0")
    private Integer priceLe;

    @ApiModelProperty(value = "店铺评分最小值（基点，如35表示3.5分），范围0-50")
    @Min(value = 0, message = "店铺评分最小值不能小于0")
    @Max(value = 50, message = "店铺评分最小值不能大于50")
    private Integer shopRatingGe;

    @ApiModelProperty(value = "店铺评分最大值（基点，如35表示3.5分），范围0-50")
    @Min(value = 0, message = "店铺评分最大值不能小于0")
    @Max(value = 50, message = "店铺评分最大值不能大于50")
    private Integer shopRatingLe;

    @ApiModelProperty(value = "销量最小值，大于等于0")
    @Min(value = 0, message = "销量最小值不能小于0")
    private Integer soldQuantityGe;

    @ApiModelProperty(value = "销量最大值，大于等于0，0或空表示无限制")
    @Min(value = 0, message = "销量最大值不能小于0")
    private Integer soldQuantityLe;

    @ApiModelProperty(value = "产品池ID列表，无title_keyword时仅支持1个")
    private List<String> poolIds;
}
