{"@@locale": "en", "home_navigation_home": "Home", "home_navigation_brand": "Brand", "home_navigation_income": "Income", "home_navigation_mine": "Mine", "login_mobile_title": "Phone Login", "login_welcome_back": "Welcome back", "login_mobile_subtitle": "Please login with your phone number", "login_phone_label": "Phone Number", "login_phone_hint": "Enter your phone number", "login_code_label": "Verification Code", "login_code_hint": "Enter the verification code", "login_get_code": "Get Code", "login_code_seconds": "s", "login_button": "<PERSON><PERSON>", "login_password_alternative": "Login with Password", "login_enter_phone": "Please enter phone number", "login_code_sent": "Verification code sent", "login_send_failed": "Failed to send code", "login_enter_phone_code": "Please enter phone number and code", "login_failed": "<PERSON><PERSON> failed", "error_title": "Oops! Something went wrong. Please try again later.", "error_button_title": "Retry", "loading_more_error": "Loading failed, try again", "loading_more_no_more": "Oh it's over!", "loading_more_empty": "No wallpaper yet ~", "loading_more_write": "write a rating", "loading_more_retry": "Retry", "home_rebate_rate_title": "Cashback Rate", "home_platform_all": "All", "home_platform_hot_sale": "Hottest", "home_platform_high_rebate": "High Rebate", "home_platform_tiktok": "TikTok", "home_platform_shopee": "<PERSON>ee", "home_logo_slogan": "Check & Earn Rebates", "home_search_placeholder": "Copy product link, get cashback", "home_search_button_title": "Paste", "home_search_instructions_copy": "Copy", "home_search_instructions_text": "Open link in GENCO to get cashback", "home_cashback_instructions_check_all": "Check All", "home_cashback_instructions_title": "<PERSON><PERSON>n <PERSON>", "home_cashback_instructions_step1": "Copy product\nlink", "home_cashback_instructions_step2": "Open GENCO\ncheck cashback", "home_cashback_instructions_step3": "Go Shopee/\nTikTok", "home_cashback_instructions_step4": "Get cashback", "home_cashback_button_title": "<PERSON><PERSON><PERSON>", "detail_price_title": "Price", "detail_sold": "Sold out", "detail_sold_count": "", "detail_cashback_amount": "Estimated Rebate Amount", "detail_rebate_rate": "Rebate Rate", "detail_cashback_flow_title": "Cashback Earning Process", "detail_cashback_flow_check": "View Tutorial", "detail_cashback_flow_step1": "Click Product", "detail_cashback_flow_step2": "Order on\nTikTok", "detail_cashback_flow_step3": "Track Order", "detail_cashback_flow_step4": "Get Cashback", "detail_brand_product_amount_pre": "Total", "detail_brand_product_amount": "products", "brand_filter_all": "All", "brand_filter_price": "Price", "brand_filter_rebate_rate": "Cashback Rate", "brand_filter_sales": "Sales", "brand_filter_latest": "New", "usage_guideline_title": "User Guide", "usage_guideline_description": "Get cashback on Shopee & TikTok in 3 steps via link", "usage_guideline_step1": "Open product on Shopee/TikTok and copy link", "usage_guideline_step2": "Paste link in GENCO to check cashback", "usage_guideline_step3": "Jump to Shopee/TikTok to complete order", "brand_home_top_title": "<red>Carefully Selected</red> Brands", "brand_home_top_subtitle": "Discover favorites while enjoying great shopping", "brand_tiktok_hot_sale_title": "TikTok Hot Brands", "brand_high_rebate_title": "High Cashback Brands", "brand_highest_rebate_rate": "Highest Cashback Rate", "message_no_data": "No Data", "income_pre_total_income": "Estimated Total Income", "income_today": "Today's Income", "income_amount_to_be_credited": "Pending Credit", "income_amount_to_be_credited_hint": "Includes unconfirmed order cashback. For reference only.", "income_amount_credited": "Credited Amount", "income_amount_credited_description": "Successfully credited amount", "income_amount_available_for_withdrawal": "<PERSON><PERSON><PERSON><PERSON> Amount", "income_withdrawal_button": "Withdraw", "income_withdrawal_success": "<PERSON><PERSON>wal Success", "income_withdrawal_failed": "Withdrawal Failed", "income_withdrawal_amount": "<PERSON><PERSON><PERSON> Amount", "income_withdrawal_amount_hint": "Enter amount", "income_transaction_history": "Transaction History", "income_transaction_history_empty": "No transactions yet", "income_transaction_detail": "Transaction Details", "income_my_order": "My Orders", "income_income_detail": "Income Details", "income_pre_total_income_description": "Estimated total income for reference. Final amount subject to actual payment.", "ok": "OK", "finish": "Complete", "credited_rebase_income": "Cashback Income", "all": "All", "income": "Income", "expenditure": "Expense", "rebase_income": "Cashback Income", "rebase_expenditure": "Cashback Expense", "withdrawal_account": "<PERSON><PERSON><PERSON> Account", "please_select_withdrawal_account": "Select account", "withdrawal_amount": "<PERSON><PERSON><PERSON> Amount", "withdrawal_amount_hint": "Max withdrawable", "withdrawal_amount_min": "Min withdrawable", "withdrawal_all": "Withdraw All", "withdrawal_finish": "<PERSON>drawal Complete", "withdrawal_success": "<PERSON><PERSON><PERSON> submitted", "withdrawal_success_hint": "Funds arrive within 24h (excl. weekends/holidays)", "withdrawal_failed": "Withdrawal Failed", "withdrawal_fees": "Fee", "withdrawal_fees_hint": "1.5% fee. Min fee Rp5.550. If <Rp5.550, fee charged at Rp5.550.", "withdrawal_hint": "Note", "withdrawal_hint_description": "Funds arrive within 24h (excl. weekends/holidays)", "trade_type": "Transaction Type", "trade_time": "Time", "trade_serial_number": "Transaction ID", "trade_channel": "Channel", "trade_order_number": "Order ID", "income_order_rebate": "Order Cashback", "income_campaign_reward": "Campaign Reward", "income_expected_total_amount": "Estimated Cashback", "income_expected_total_amount_hint": "Estimated, subject to final credit", "income_actual_credited_amount": "Actual Cashback", "income_actual_credited_amount_hint": "Credited to account", "order_title": "My Orders", "order_tab_all": "All", "order_tab_processing": "Processing", "order_tab_completed": "Completed", "order_tab_expired": "Expired", "order_application_time": "Application Time:", "order_status_processing": "Processing", "order_status_completed": "Completed", "order_status_expired": "Expired", "order_expected_cashback": "Est. Cashback:", "order_cashback_info": "Cashback Info", "rebate_instruction_title": "Cashback Guide", "rebate_instruction_content": "Cashback credited after order confirmation & completion.", "rebate_step_1_title": "01", "rebate_step_1_content": "Order via Shopee/TikTok", "rebate_step_2_title": "02", "rebate_step_2_content": "Track status in 1-3 days", "rebate_step_3_title": "03", "rebate_step_3_content": "Get cashback after completion", "rebate_how_to_get_title": "How to <PERSON>arn <PERSON>:", "rebate_how_to_order_title": "How to Order", "rebate_how_to_order_content": "Place order via GENCO redirect to track cashback.", "rebate_genco_last_app_title": "Keep GENCO as Last App", "rebate_genco_last_app_content": "Cashback untraceable if other apps opened after GENCO.", "rebate_unsupported_order_title": "Ineligible Orders", "rebate_unsupported_order_content": "Orders not placed via GENCO redirect are ineligible.", "rebase_info": "Cashback Details", "rebase_price": "Purchase Price", "rebase_rate": "Cashback Rate", "rebase_cash": "Cashback Earned", "withdrawal_choose_method": "Select Account", "withdrawal_add_card": "Add Bank Card", "withdrawal_add_e_card": "Add e-Wallet", "name": "Name", "name_placeholder": "Enter name", "bank_name": "Bank Name", "select_bank": "Select Bank", "bank_card_number": "Card Number", "input_bank_card_number": "Enter card number", "search": "Search", "button_next": "Continue", "bind_bank_card_confirm": "Confirm Card", "e_wallet": "E-Wallet", "phone_number": "Phone", "phone_number_placeholder": "Enter Phone Number", "select_e_wallet": "Select e-Wallet", "usage_hint": "Save with GENCO", "my_collection": "My Collections", "guide_step1_title": "4 Steps to Cashback", "guide_step1_content_flow_1": "Copy product link on Shopee/TikTok", "guide_step1_content_flow_2": "Check cashback in GENCO", "guide_step1_content_flow_3": "Order on Shopee/TikTok", "guide_step1_content_flow_4": "Confirm order & get cashback", "guide_step1_content_flow_1_title": "Copy Product Link", "guide_step1_content_flow_1_description": "Tap 'Share' > 'Copy Link' on Shopee/TikTok", "guide_step1_content_flow_2_title": "Check Cashback in GENCO", "guide_step1_content_flow_2_description": "Paste link in GENCO to see cashback", "guide_step1_content_flow_3_title": "Tap 'Order on Shopee/TikTok'", "guide_step1_content_flow_3_description": "Jump to Shopee/TikTok to buy", "guide_step1_content_flow_4_title": "Track & Get Cashback", "guide_step1_content_flow_4_description": "Cashback credited after order confirmation", "guide_step1_content_flow_5_title": "How to Get <PERSON><PERSON>?", "guide_step1_content_flow_5_description": "Watch tutorial video for details", "feedback_cash": "Cashback", "edit": "Edit", "cancel": "Cancel", "select_all": "Select All", "cancel_select_all": "Deselect All", "delete": "Delete", "setting": "Settings", "my_avatar": "My Avatar", "nickname": "Nickname", "whatsapp_account": "WhatsApp", "modify_phone_number": "Change Phone", "modify_password": "Change Password", "privacy": "Privacy", "about": "About", "modify_nickname": "Change Nickname", "login_with_tiktok": "Login with TikTok", "login_title": "Welcome to GENCO", "login_subtitle": "Save on shopping,\nEarn by sharing", "whatsapp_account_hint": "Enter WhatsApp number", "next_step": "Next", "account_empty_hint": "Account required", "input_opt_verification_code": "Enter OTP", "input_opt_verification_code_hint": "Sent to WhatsApp {phone}", "@input_opt_verification_code_hint": {"description": "提示用户验证码已发送到哪个WhatsApp号码", "placeholders": {"phone": {"type": "String", "example": "**********"}}}, "login_with_password": "Password Login", "resend_in": "Resend in", "seconds": "s", "resend_code": "Resend Code", "login": "<PERSON><PERSON>", "input_opt_verification_code_error": "Enter code", "login_success": "Login Success", "welcome_back": "Welcome back!", "please_input_your_password": "Enter password", "login_password": "Password", "input_password_hint": "Enter password", "login_with_verification_code": "Code Login", "login_agreement": "By logging in, you agree to the", "and": "and", "user_agreement": "Terms of Service", "privacy_policy": "Privacy Policy", "setting_login_password": "Set Password", "set_password_hint": "6-20 letters + numbers", "login_password_confirm": "Confirm Password", "login_password_confirm_hint": "Re-enter password", "password_not_same": "Passwords don't match", "modify_success": "Updated", "find_product": "Found Product", "check_cash_back": "View Cashback", "back": "Back", "can_not_open_link": "Can't open link", "collection": "Collection", "order_right_now": "Order Now", "add_to_collection_success": "Added to Collections", "please_select_bank_or_e_wallet": "Select bank/e-wallet", "please_input_amount": "Enter amount", "please_select_bank": "Select bank", "please_input_bank_number": "Enter card number", "please_select_e_wallet": "Select e-wallet", "please_input_e_wallet_account": "Enter e-wallet", "logout": "Logout", "logout_confirm_title": "Confirm <PERSON>ut", "logout_confirm_message": "Log out now?", "confirm": "Confirm", "jump_to_tiktok": "Will jump to TikTok", "share_text": "Place your order immediately through GENCO to receive an estimated cashback of Rp {amount}", "@share_text": {"description": "分享产品的文本", "placeholders": {"amount": {"type": "String", "example": "0"}}}, "nickname_hint": "Please input nickname", "member_introduction": "Upgrade to become an agent or partner to earn more", "member_level_state": "Level Status", "member_status_description": "You are not currently an agent or partner", "member_level_silver_agent": "Silver Agent", "member_level_partner": "Partner", "member_level_silver_agent_fee": "Silver Agent <PERSON>e", "member_level_partner_fee": "Partner Fee", "become_member": "Become Agent", "year": "Year", "delete_account": "Deactivate Account", "delete_account_title": "Confirm Account Deletion?", "delete_account_content": "After account deletion, all your privileges and benefits will be permanently lost,\nand you will permanently lose access to your account.", "product_link_empty": "No product found for the link", "product_link_empty_content": "No matching product found for this link. Please check the link or try another product.", "exclusive_benefits": "Exclusive Benefits", "member_benefits_silver_agent_1": "Validity Period", "member_benefits_silver_agent_1_value": "1 Year", "member_benefits_silver_agent_2": "Referral Bonus", "member_benefits_silver_agent_2_value": "Get extra rewards for successful referrals", "member_benefits_silver_agent_3": "Team Shopping Commission", "member_benefits_silver_agent_3_value": "Earn commissions from your team's purchases", "member_benefits_silver_agent_4": "Extra Cashback", "member_benefits_silver_agent_4_value": "Higher cashback up to 50%", "member_benefits_silver_agent_5": "Unlimited Cashback", "member_benefits_silver_agent_5_value": "Cashback without limits", "member_benefits_partner_agent_1": "Validity Period", "member_benefits_partner_agent_1_value": "Permanent", "member_benefits_partner_agent_2": "Referral Bonus", "member_benefits_partner_agent_2_value": "Receive high bonuses up to 1 billion+ upon success", "member_benefits_partner_agent_3": "Team Shopping Commission", "member_benefits_partner_agent_3_value": "Earn up to 20% cashback from each downline's cashback", "member_benefits_partner_agent_4": "Extra Cashback", "member_benefits_partner_agent_4_value": "Higher cashback up to 100%", "role": "Role", "benefit": "Benefit", "normal_user": "Regular User", "normal_user_2_benefit": "No bonus", "agent": "Agent", "member_benefits_silver_agent_2_benefit": "Can earn up to 10,000,000 IDR bonus", "normal_user_3_benefit": "No shopping commission", "member_benefits_silver_agent_3_benefit": "10% commission from direct referrals' shopping cashback (5% regular + 5% promotion)", "normal_user_4_benefit": "No shopping commission", "member_benefits_silver_agent_4_benefit": "10% commission from direct referrals' shopping cashback (5% regular + 5% promotion)", "normal_user_5_benefit": "No shopping commission", "member_benefits_silver_agent_5_benefit": "10% commission from direct referrals' shopping cashback (5% regular + 5% promotion)", "order_payment": "Order Payment", "order_price": "Order Amount", "product_name": "Product Name", "real_payment_price": "Actual Payment Amount", "agent_fee": "Agent <PERSON>", "purchase_right_now": "Pay Now", "payment_problem": "Payment Issue", "payment_complete": "Completed", "payment_agreement": "Read and Agree", "payment_agreement_link": "Payment Terms", "cashback_is_0": "This product has no cashback", "cashback_is_0_content": "There is no cashback for this product. Do you want to continue?", "nickname_too_long": "Nickname is too long, maximum 10 characters", "check_payment_result": "Checking Payment Result", "payment_amount": "Payment Amount", "payment_id": "Payment Transaction ID", "payment_method": "Payment Method", "payment_success": "Payment Successful", "payment_failed": "Payment Failed", "level_status": "Level Status", "valid_for": "Valid for: 1 year", "upgrade_date": "Upgrade Date: June 11, 2025", "to_gold_progress": "Progress to Gold Agent", "invite_to_upgrade": "Invite 10 friends to become Silver Agents or higher", "silver_agent": "Silver Agent", "gold_agent": "Gold Agent", "diamond_agent": "Diamond Agent", "partner": "Partner", "direct_invite_reward": "Direct Invitation Reward", "direct_invite_detail": "Invite 1 agent to get 35,000 IDR reward. Invite 3 to break even!", "team_purchase_bonus": "Team Purchase Commission", "team_purchase_detail": "Earn 10% commission on shopping of your directly invited team", "team_purchase_detail_gold": "Indirect invitation (level 2): Each accumulates 15,000 IDR; Indirect invitation (level 3): Each accumulates 10,000 IDR.", "training": "Training", "training_detail": "Professional mentors provide high-quality courses and guidance", "extra_cashback": "Extra Cashback", "extra_cashback_detail_gold": "For every 10 Gold Agents developed: 300,000 IDR reward", "extra_cashback_detail": "Enjoy extra cashback benefits during specified periods", "invite_to_upgrade_empty": "Invite 10 new friends to become Silver Agents or higher.\\nYour status will upgrade automatically!", "silver_partner": "Silver Partner", "gold_partner": "Gold Partner", "diamond_partner": "Diamond Partner", "partner_extra_bonus1": "For every 10 Silver Partners developed: 1,000,000 IDR reward", "partner_extra_bonus2": "For every 10 Gold Partners developed: 2,000,000 IDR reward", "direct_invite_detail2": "Reward 200,000 IDR per agent invited, break even by inviting just 3!", "team_purchase_detail_gold2": "Indirect invitation (level 2): Each gets 100,000 IDR; Indirect invitation (level 3): Each gets 50,000 IDR", "extra_bonus": "Extra Bonus", "normal_member": "Regular Member", "high_cashback": "High Cashback", "high_cashback_description": "Spend more, save more", "no_limit": "Unlimited", "no_limit_description": "Enjoy unlimited cashback", "user_service": "Customer Service", "user_service_description": "Premium customer service", "invite_and_eran_bonus": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "invite_code": "Invitation Code", "input_invite_code": "Enter Invitation Code", "contact_up": "Contact Supervisor", "congratulation_to_add_group": "Congratulations on joining", "group": "'s team", "my_team": "My Team", "task_center": "Task Center", "task_center_title": "Task Center", "task_cash_income": "Cash Income (Rp)", "task_withdraw": "Withdraw", "task_withdrawable_amount": "<PERSON><PERSON><PERSON><PERSON> Amount", "task_daily_tasks": "Task List", "task_invite_reward": "Invite First Order Reward", "task_invite_progress": "Invite Progress", "task_order_progress": "Order Progress", "task_invite_count": "Invite <PERSON>", "task_order_count": "Order Count", "task_conditions_met": "Conditions Met", "task_conditions_not_met": "Conditions Not Met", "task_go_claim": "<PERSON><PERSON><PERSON>", "task_feature_developing": "Feature Developing", "task_developing": "Task Feature Developing", "task_return_cash_welfare": "Return Cash Welfare", "task_return_cash_welfare_desc": "Daily Exclusive", "task_view_record": "View Record", "task_record_title": "Task Record", "task_total_invites": "Total Invites", "task_redeemed_invites": "Redeemed <PERSON><PERSON><PERSON>", "task_total_orders": "Total Orders", "task_redeemed_orders": "Redeemed Orders", "task_close": "Close", "task_reward_amount": "<PERSON><PERSON> Amount", "task_per_completion": "Per Completion", "invite_bonus": "Referral Bonus", "shopping_bonus": "Shopping Bonus", "cumulative_number_of_invitations": "Total Invitations", "today": "Today", "invite_agent": "Invite Agent", "invite_normal_user": "Invite Regular User", "silver": "Silver", "gold": "Gold", "diamond": "Diamond", "team_support": "Team Contribution", "received_bonus": "Received Bonus", "invite_time": "Invitation Time", "pre_team_cashback": "Estimated Team Contribution Cashback", "invite_and_earn_money": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>n Money", "level_up_schedule": "Level Up Progress", "level_up_description": "Upgrade Instructions", "level_up_content_title": "Invite 10 friends to become Silver Agents or higher", "level_up_description_title": "IDR in Hand", "level_up_description_title1": "Just develop 10 Diamond Agents!", "invite_gold_agent": "Invite Gold Agent", "invite_diamond_agent": "Invite Diamond Agent", "level_up_bonus": "Level Up Progress & Rewards", "activity_rule": "Activity Rules", "bonus": "<PERSON><PERSON>", "direct_invite_detail3": "Invite agents: 35,000 IDR reward per person", "team_bonus": "Team Bonus", "team_bonus_detail": "Indirect referral (level 2): Each accumulates 100,000 IDR;\nIndirect referral (level 3): Each accumulates 50,000 IDR;\nIf you reach \"Gold Partner\" status (successfully refer 10 partners directly), bonus becomes withdrawable. Valid for 60 days from generation. If not achieved, bonus expires.", "partner_extra_bonus3": "For every 10 Gold Partners developed: 2,000,000 IDR bonus;\nFor every 10 Diamond Partners developed: 100,000,000 IDR bonus.", "invite_code_empty_hint": "Invitation code cannot be empty!", "copy_success": "Copied Successfully", "network_is_not_available": "Network is unavailable, please check your connection", "login_with_other_method": "Login with other method", "member_introduction_level_silver_agent": "Silver", "normal_member_user": "Normal", "agree_with_payment_term": "Do you agree with the payment terms?", "please_choose_payment_method": "Please select a payment method", "qrcode": "QRCode", "open_payment_link": "Open Payment Link Directly", "pay_with_qrcode": "Pay with QR Code", "pay_with_qrcode_usage": "Scan the QR code to open the payment link and pay. If you want to pay directly in this app, tap to open the link.", "jump_link_failed": "Jump Link Failed", "login_expired_hint": "<PERSON><PERSON> has expired, please log in again!", "network_error": "Network error", "unknown_error": "Unknown error"}