  Activity android.app  Intent android.content  NonNull androidx.annotation  AuthApi com.tiktok.open.sdk.auth  AuthRequest com.tiktok.open.sdk.auth  AuthResponse com.tiktok.open.sdk.auth  
AuthMethod  com.tiktok.open.sdk.auth.AuthApi  	authorize  com.tiktok.open.sdk.auth.AuthApi  getAuthResponseFromIntent  com.tiktok.open.sdk.auth.AuthApi  	ChromeTab +com.tiktok.open.sdk.auth.AuthApi.AuthMethod  authCode %com.tiktok.open.sdk.auth.AuthResponse  	errorCode %com.tiktok.open.sdk.auth.AuthResponse  errorMsg %com.tiktok.open.sdk.auth.AuthResponse  grantedPermissions %com.tiktok.open.sdk.auth.AuthResponse  let %com.tiktok.open.sdk.auth.AuthResponse  state %com.tiktok.open.sdk.auth.AuthResponse  	PKCEUtils com.tiktok.open.sdk.auth.utils  generateCodeVerifier (com.tiktok.open.sdk.auth.utils.PKCEUtils  Activity com.tofinds.tiktok_sdk_v2  
ActivityAware com.tofinds.tiktok_sdk_v2  ActivityPluginBinding com.tofinds.tiktok_sdk_v2  AuthApi com.tofinds.tiktok_sdk_v2  AuthRequest com.tofinds.tiktok_sdk_v2  Boolean com.tofinds.tiktok_sdk_v2  
FlutterPlugin com.tofinds.tiktok_sdk_v2  Intent com.tofinds.tiktok_sdk_v2  
MethodCall com.tofinds.tiktok_sdk_v2  MethodCallHandler com.tofinds.tiktok_sdk_v2  
MethodChannel com.tofinds.tiktok_sdk_v2  NonNull com.tofinds.tiktok_sdk_v2  	PKCEUtils com.tofinds.tiktok_sdk_v2  PluginRegistry com.tofinds.tiktok_sdk_v2  Result com.tofinds.tiktok_sdk_v2  String com.tofinds.tiktok_sdk_v2  TiktokSdkV2Plugin com.tofinds.tiktok_sdk_v2  generateCodeVerifier com.tofinds.tiktok_sdk_v2  
isNotEmpty com.tofinds.tiktok_sdk_v2  let com.tofinds.tiktok_sdk_v2  mapOf com.tofinds.tiktok_sdk_v2  println com.tofinds.tiktok_sdk_v2  to com.tofinds.tiktok_sdk_v2  FlutterPluginBinding 'com.tofinds.tiktok_sdk_v2.FlutterPlugin  NewIntentListener (com.tofinds.tiktok_sdk_v2.PluginRegistry  AuthApi +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  AuthRequest +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  
MethodChannel +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  	PKCEUtils +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  activity +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  activityPluginBinding +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  authApi +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  bindActivityBinding +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  channel +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  	clientKey +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  codeVerifier +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  generateCodeVerifier +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  
isNotEmpty +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  let +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  loginResult +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  mapOf +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  println +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  redirectUrl +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  to +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  unbindActivityBinding +com.tofinds.tiktok_sdk_v2.TiktokSdkV2Plugin  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  addOnNewIntentListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  removeOnNewIntentListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  NewIntentListener 'io.flutter.plugin.common.PluginRegistry  	Function1 kotlin  Nothing kotlin  Pair kotlin  let kotlin  to kotlin  toString 
kotlin.Int  
isNotEmpty 
kotlin.String  to 
kotlin.String  Map kotlin.collections  
isNotEmpty kotlin.collections  mapOf kotlin.collections  println 	kotlin.io  
isNotEmpty kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        