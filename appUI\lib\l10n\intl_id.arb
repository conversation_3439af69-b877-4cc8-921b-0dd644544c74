{"@@locale": "id", "home_navigation_home": "Be<PERSON><PERSON>", "home_navigation_brand": "Brand", "home_navigation_income": "Pendapatan", "home_navigation_mine": "<PERSON><PERSON>", "login_mobile_title": "Masuk via Nomor Ponsel", "login_welcome_back": "Selamat Datang Kembali", "login_mobile_subtitle": "<PERSON><PERSON><PERSON> masuk dengan nomor ponsel", "login_phone_label": "<PERSON><PERSON>", "login_phone_hint": "<PERSON><PERSON><PERSON><PERSON> nomor ponsel", "login_code_label": "<PERSON><PERSON>", "login_code_hint": "Masukkan kode verifikasi", "login_get_code": "Dapatkan Kode", "login_code_seconds": "detik", "login_button": "<PERSON><PERSON><PERSON>", "login_password_alternative": "<PERSON><PERSON><PERSON>", "login_enter_phone": "<PERSON><PERSON> masukkan nomor ponsel", "login_code_sent": "Kode verifikasi telah di<PERSON>rim", "login_send_failed": "Pengiriman kode gagal", "login_enter_phone_code": "<PERSON><PERSON><PERSON>n nomor ponsel dan kode", "login_failed": "<PERSON><PERSON> ma<PERSON>k", "error_title": "Maaf! <PERSON><PERSON><PERSON><PERSON> k<PERSON>. <PERSON><PERSON>an coba lagi.", "error_button_title": "<PERSON><PERSON>", "loading_more_error": "<PERSON><PERSON> memuat, silakan coba lagi", "loading_more_no_more": "<PERSON>dah sampai akhir~", "loading_more_empty": "Belum ada wallpaper ~", "loading_more_write": "<PERSON><PERSON>", "loading_more_retry": "<PERSON><PERSON>", "home_rebate_rate_title": "Persentase Cashback", "home_platform_all": "<PERSON><PERSON><PERSON>", "home_platform_hot_sale": "<PERSON><PERSON><PERSON>", "home_platform_high_rebate": "Cash<PERSON> <PERSON><PERSON><PERSON>", "home_platform_tiktok": "TikTok", "home_platform_shopee": "<PERSON>ee", "home_logo_slogan": "Cari & Bandingkan, Dapat Cashback!", "home_search_placeholder": "<PERSON><PERSON>an produk, dapat<PERSON> cashback", "home_search_button_title": "Tempel", "home_search_instructions_copy": "<PERSON><PERSON>", "home_search_instructions_text": "<PERSON><PERSON> <PERSON><PERSON> di GENCO, raih cashback", "home_cashback_instructions_title": "<PERSON> Cashback", "home_cashback_instructions_check_all": "<PERSON><PERSON>", "home_cashback_instructions_step1": "<PERSON><PERSON>\ntautan produk", "home_cashback_instructions_step2": "Buka GENCO\nCek cashback", "home_cashback_instructions_step3": "Beli di\nShopee/TikTok", "home_cashback_instructions_step4": "Dapatkan Cashback", "home_cashback_button_title": "<PERSON><PERSON><PERSON><PERSON> jumlah cashback", "detail_price_title": "<PERSON><PERSON>", "detail_sold": "<PERSON><PERSON><PERSON><PERSON>", "detail_sold_count": "pcs", "detail_cashback_amount": "<PERSON><PERSON><PERSON><PERSON>", "detail_rebate_rate": "Persentase Cashback", "detail_cashback_flow_title": "Alur Dapatkan Cashback", "detail_cashback_flow_check": "<PERSON><PERSON>", "detail_cashback_flow_step1": "Klik produk", "detail_cashback_flow_step2": "Pesan via\nTikTok", "detail_cashback_flow_step3": "<PERSON><PERSON>", "detail_cashback_flow_step4": "Dapatkan\nCashback", "detail_brand_product_amount_pre": "Total", "detail_brand_product_amount": "produk", "brand_filter_all": "<PERSON><PERSON><PERSON>", "brand_filter_price": "<PERSON><PERSON>", "brand_filter_rebate_rate": "Cashback", "brand_filter_sales": "Penjualan", "brand_filter_latest": "<PERSON><PERSON>", "usage_guideline_title": "Panduan Pengguna", "usage_guideline_description": "Dapatkan cashback di Shopee/TikTok dalam 3 langkah mudah!", "usage_guideline_step1": "<PERSON><PERSON> produk di <red><PERSON>ee</red> atau <red>TikTok</red>, salin tautan produk", "usage_guideline_step2": "<PERSON><PERSON> <red>GENCO</red>, tempel tautan, lihat cashback", "usage_guideline_step3": "<PERSON><PERSON> ke <red><PERSON>ee</red>/<red>TikTok</red> via GENCO, selesaikan pembelian", "brand_home_top_title": "Brand Pilihan <red>Premium</red>", "brand_home_top_subtitle": "Temukan favoritmu dengan harga terbaik & cashback!", "brand_tiktok_hot_sale_title": "Brand Populer di TikTok", "brand_high_rebate_title": "Brand Cashback <PERSON><PERSON><PERSON>", "brand_highest_rebate_rate": "Cashback Te<PERSON><PERSON>", "message_no_data": "Belum ada data", "income_pre_total_income": "Pendapatan Diterima", "income_today": "Pendapatan Hari Ini", "income_amount_to_be_credited": "<PERSON><PERSON><PERSON>", "income_amount_to_be_credited_hint": "Termasuk cashback p<PERSON>an yang belum dikonfirmasi. <PERSON><PERSON>.", "income_amount_credited": "<PERSON>", "income_amount_credited_description": "<PERSON><PERSON><PERSON> yang sudah diterima", "income_amount_available_for_withdrawal": "<PERSON><PERSON> yang dapat ditarik", "income_withdrawal_button": "<PERSON><PERSON>", "income_withdrawal_success": "Penarikan Berhasil!", "income_withdrawal_failed": "Penarikan <PERSON>", "income_withdrawal_amount": "<PERSON><PERSON><PERSON>", "income_withdrawal_amount_hint": "<PERSON><PERSON><PERSON><PERSON> jumlah penarikan", "income_transaction_history": "Riwayat Transaksi", "income_transaction_history_empty": "Belum ada transaksi", "income_transaction_detail": "Detail Transaksi", "income_my_order": "<PERSON><PERSON><PERSON>", "income_income_detail": "Detail Pendapatan", "income_pre_total_income_description": "Perkiraan total pendapatan sebagai referensi. <PERSON><PERSON><PERSON> akhir akan disesuaikan dengan pembayaran aktual", "ok": "OK", "finish": "Se<PERSON><PERSON>", "credited_rebase_income": "Cashback <PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "income": "<PERSON><PERSON><PERSON><PERSON>", "expenditure": "<PERSON><PERSON><PERSON><PERSON>", "rebase_income": "Cashback <PERSON><PERSON><PERSON>", "rebase_expenditure": "Cash<PERSON> <PERSON><PERSON><PERSON>", "withdrawal_account": "<PERSON><PERSON><PERSON>", "please_select_withdrawal_account": "<PERSON><PERSON><PERSON> akun penarikan", "withdrawal_amount": "<PERSON><PERSON><PERSON> maksimum yang bisa ditarik", "withdrawal_amount_hint": "<PERSON><PERSON><PERSON> yang bisa ditarik", "withdrawal_amount_min": "Minimal penarikan", "withdrawal_all": "<PERSON><PERSON>", "withdrawal_finish": "<PERSON><PERSON><PERSON>", "withdrawal_success": "Penarikan ber<PERSON>il di<PERSON>ukan!", "withdrawal_success_hint": "<PERSON> masuk ≤24 jam kerja. Cek saldo akun <PERSON>!", "withdrawal_failed": "Penarikan <PERSON>", "withdrawal_fees": "<PERSON><PERSON><PERSON>", "withdrawal_fees_hint": "Biaya 1.5% dari penarikan. Minimal Rp5.550. <PERSON><PERSON> <Rp5.550, biaya tetap Rp5.550.", "withdrawal_hint": "Catatan", "withdrawal_hint_description": "<PERSON> masuk ≤24 jam kerja (<PERSON><PERSON>/<PERSON>/libur)", "trade_type": "<PERSON><PERSON>", "trade_time": "<PERSON><PERSON><PERSON>", "trade_serial_number": "<PERSON><PERSON>", "trade_channel": "Channel", "trade_order_number": "<PERSON> Pesanan", "income_order_rebate": "Cash<PERSON> <PERSON><PERSON><PERSON>", "income_campaign_reward": "Hadiah Event", "income_expected_total_amount": "Total Estimasi Cashback", "income_expected_total_amount_hint": "Estimasi cashback p<PERSON><PERSON>. <PERSON><PERSON><PERSON> berubah jika ada refund.", "income_actual_credited_amount": "<PERSON><PERSON><PERSON>", "income_actual_credited_amount_hint": "Sudah Masuk ke Akun", "order_title": "<PERSON><PERSON><PERSON>", "order_tab_all": "<PERSON><PERSON><PERSON>", "order_tab_processing": "Diproses", "order_tab_completed": "Se<PERSON><PERSON>", "order_tab_expired": "Kadaluarsa", "order_application_time": "<PERSON><PERSON><PERSON> permin<PERSON>:", "order_status_processing": "Diproses", "order_status_completed": "Se<PERSON><PERSON>", "order_status_expired": "Kadaluarsa", "order_expected_cashback": "Estimasi cashback:", "order_cashback_info": "<PERSON><PERSON><PERSON><PERSON>", "rebate_instruction_title": "<PERSON><PERSON><PERSON>", "rebate_instruction_content": "Cashback masuk ke saldo GENCO setelah pesanan dikonfirmasi di Shopee/TikTok. Proses verifikasi: 1-3 hari.", "rebate_step_1_title": "01", "rebate_step_1_content": "Pesan via Shopee/TikTok lewat link GENCO.", "rebate_step_2_title": "02", "rebate_step_2_content": "GENCO verifikasi otomatis (1-3 hari).", "rebate_step_3_title": "03", "rebate_step_3_content": "Cashback masuk set<PERSON>h pesanan SELESAI.", "rebate_how_to_get_title": "<PERSON> Cashback:", "rebate_how_to_order_title": "CARA BELI", "rebate_how_to_order_content": "Setiap produk HARUS diakses lewat GENCO sebelum checkout.", "rebate_genco_last_app_title": "GENCO HARUS App Terakhir", "rebate_genco_last_app_content": "Cashback tidak dapat dilacak jika membuka aplikasi lain setelah GENCO", "rebate_unsupported_order_title": "<PERSON><PERSON><PERSON>", "rebate_unsupported_order_content": "Pesanan di luar tautan GENCO tidak memenuhi syarat.", "rebase_info": "Detail Cashback", "rebase_price": "<PERSON><PERSON>", "rebase_rate": "Persentase Cashback", "rebase_cash": "Cashback yang <PERSON>", "withdrawal_choose_method": "<PERSON><PERSON><PERSON>", "withdrawal_add_card": "+ Rekening Bank", "withdrawal_add_e_card": "+ Tambah E-Wallet", "name": "<PERSON><PERSON>", "name_placeholder": "<PERSON><PERSON> se<PERSON> rekening", "bank_name": "Nama Bank", "select_bank": "Pilih Bank", "bank_card_number": "Nomor <PERSON>", "input_bank_card_number": "<PERSON><PERSON><PERSON><PERSON> nomor rekening", "search": "<PERSON><PERSON>", "button_next": "<PERSON><PERSON><PERSON><PERSON>", "bind_bank_card_confirm": "Konfirmasi Kartu", "e_wallet": "E-Wallet", "phone_number": "Nomor Telepon", "phone_number_placeholder": "Masukkan Nomor Telepon", "select_e_wallet": "<PERSON><PERSON><PERSON>", "usage_hint": "Hemat dengan GENCO!", "my_collection": "<PERSON><PERSON><PERSON><PERSON>", "guide_step1_title": "4 Langkah Dapat Cashback", "guide_step1_content_flow_1": "<PERSON><PERSON>ee/<PERSON><PERSON><PERSON>, salin tautan produk", "guide_step1_content_flow_2": "<PERSON><PERSON> GENCO, cek cashback", "guide_step1_content_flow_3": "Klik ke Shopee/TikTok, beli", "guide_step1_content_flow_4": "Cashback ma<PERSON><PERSON>", "guide_step1_content_flow_1_title": "SALIN TAUTAN PRODUK", "guide_step1_content_flow_1_description": "Di Shopee/TikTok: Klik 'Bagikan' > '<PERSON><PERSON>'.", "guide_step1_content_flow_2_title": "CEK CASHBACK DI GENCO", "guide_step1_content_flow_2_description": "Buka GENCO > Tempel otomatis atau ketik manual.", "guide_step1_content_flow_3_title": "BELI VIA GENCO", "guide_step1_content_flow_3_description": "<PERSON><PERSON> '<PERSON><PERSON> di <PERSON>ee/TikTok' > Checkout se<PERSON>i biasa.", "guide_step1_content_flow_4_title": "DAPATKAN CASHBACK", "guide_step1_content_flow_4_description": "Cashback otomatis ma<PERSON>k <PERSON> '<PERSON><PERSON><PERSON>/TikTok.", "guide_step1_content_flow_5_title": "Panduan Video", "guide_step1_content_flow_5_description": "Tonton video cara klaim cashback!", "feedback_cash": "Feedback <PERSON>back", "edit": "Edit", "cancel": "<PERSON><PERSON>", "select_all": "<PERSON><PERSON><PERSON>", "cancel_select_all": "<PERSON><PERSON><PERSON>", "delete": "Hapus", "setting": "<PERSON><PERSON><PERSON><PERSON>", "my_avatar": "Foto Profil", "nickname": "<PERSON><PERSON>", "whatsapp_account": "<PERSON><PERSON><PERSON>", "modify_phone_number": "Ubah Nomor Ponsel", "modify_password": "Ubah Kata Sandi", "privacy": "<PERSON><PERSON><PERSON><PERSON>", "about": "Tentang", "modify_nickname": "Ubah Nickname", "login_with_tiktok": "Masuk dengan T<PERSON>", "login_title": "Selamat Datang di GENCO", "login_subtitle": "<PERSON><PERSON>,\nBagikan Dapat Du<PERSON>", "whatsapp_account_hint": "<PERSON><PERSON><PERSON><PERSON> nomor WhatsApp Anda", "next_step": "<PERSON><PERSON><PERSON><PERSON>", "account_empty_hint": "Nomor WhatsApp tidak boleh kosong", "input_opt_verification_code": "Masukkan Kode OTP", "input_opt_verification_code_hint": "Kode verifikasi dikirim ke WhatsApp {phone}", "@input_opt_verification_code_hint": {"description": "Nomor WhatsApp penerima kode OTP", "placeholders": {"phone": {"type": "String", "example": "**********"}}}, "login_with_password": "<PERSON><PERSON><PERSON>", "resend_in": "<PERSON><PERSON>", "seconds": " detik", "resend_code": "<PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "input_opt_verification_code_error": "Harap masukkan kode OTP", "login_success": "<PERSON><PERSON><PERSON><PERSON>", "welcome_back": "Hai, selamat datang kembali!", "please_input_your_password": "<PERSON><PERSON><PERSON><PERSON> kata sandi <PERSON>a", "login_password": "<PERSON><PERSON>", "input_password_hint": "<PERSON>a sandi <PERSON>", "login_with_verification_code": "Masuk dengan Kode OTP", "login_agreement": "<PERSON><PERSON>, <PERSON><PERSON>", "and": "dan", "user_agreement": "<PERSON><PERSON><PERSON>", "privacy_policy": "<PERSON><PERSON><PERSON><PERSON>", "setting_login_password": "<PERSON><PERSON>", "set_password_hint": "Sandi: 6-20 karakter (huruf + angka)", "login_password_confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "login_password_confirm_hint": "<PERSON><PERSON><PERSON><PERSON> kembali sandi", "password_not_same": "Sandi tidak cocok", "modify_success": "<PERSON><PERSON><PERSON><PERSON>", "find_product": "<PERSON><PERSON><PERSON>", "check_cash_back": "<PERSON><PERSON>", "back": "Kembali", "can_not_open_link": "Tautan Tidak Bisa Dibuka", "collection": "<PERSON><PERSON><PERSON><PERSON>", "order_right_now": "<PERSON><PERSON>", "add_to_collection_success": "Ditambahkan ke Favorit", "please_select_bank_or_e_wallet": "Pilih Bank/Dompet Digital", "please_input_amount": "<PERSON><PERSON><PERSON><PERSON> jumlah penarikan", "please_select_bank": "Pilih bank", "please_input_bank_number": "<PERSON><PERSON><PERSON><PERSON> nomor rekening", "please_select_e_wallet": "Pilih dompet digital", "please_input_e_wallet_account": "Masukkan akun dompet digital", "logout": "<PERSON><PERSON><PERSON>", "logout_confirm_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logout_confirm_message": "Yakin ingin keluar dari akun?", "confirm": "OK", "jump_to_tiktok": "Se<PERSON>a dial<PERSON> ke TikTok", "share_text": "Segera pesan melalui GENCO untuk memperoleh estimasi cashback sebesar Rp{amount}.", "@share_text": {"description": "分享产品的文本", "placeholders": {"amount": {"type": "String", "example": "0"}}}, "nickname_hint": "<PERSON><PERSON><PERSON> masukkan nama panggilan", "member_introduction": "Tingkatkan menjadi agen atau mitra untuk menghasilkan lebih banyak", "member_level_state": "Status Level", "member_status_description": "Anda saat ini belum menjadi agen atau mitra", "member_level_silver_agent": "<PERSON><PERSON>", "member_level_partner": "<PERSON><PERSON>", "member_level_silver_agent_fee": "Biaya Agen <PERSON>", "member_level_partner_fee": "Biaya Mitra", "become_member": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>", "delete_account": "Nonaktifkan Akun", "delete_account_title": "Konfirmasi <PERSON>?", "delete_account_content": "<PERSON><PERSON><PERSON> akun <PERSON>, semua hak dan manfaat terkait <PERSON>a akan hilang secara permanen,\ndan tidak akan bisa masuk lagi ke akun ini.", "product_link_empty": "Tidak menemukan produk untuk link tersebut", "product_link_empty_content": "Tidak ditemukan produk yang sesuai untuk tautan produk ini. <PERSON><PERSON> periksa apakah tautannya benar, atau coba produk lain.", "exclusive_benefits": "Manfaat Eksklusif", "member_benefits_silver_agent_1": "<PERSON><PERSON>", "member_benefits_silver_agent_1_value": "1 Tahun", "member_benefits_silver_agent_2": "Bonus Undangan", "member_benefits_silver_agent_2_value": "<PERSON><PERSON><PERSON>, da<PERSON><PERSON><PERSON> hadiah tambahan", "member_benefits_silver_agent_3": "<PERSON><PERSON><PERSON>", "member_benefits_silver_agent_3_value": "<PERSON><PERSON><PERSON> tim, <PERSON><PERSON><PERSON> keunt<PERSON>an komisi", "member_benefits_silver_agent_4": "Cash<PERSON> <PERSON>", "member_benefits_silver_agent_4_value": "Cashback lebih tinggi, hing<PERSON> 50%", "member_benefits_silver_agent_5": "Cashback <PERSON><PERSON>", "member_benefits_silver_agent_5_value": "Cashback tanpa batas", "member_benefits_partner_agent_1": "<PERSON><PERSON>", "member_benefits_partner_agent_1_value": "<PERSON><PERSON><PERSON>", "member_benefits_partner_agent_2": "Bonus Undangan", "member_benefits_partner_agent_2_value": "Dapatkan bonus tinggi 10 miliar+ jika berhasil", "member_benefits_partner_agent_3": "<PERSON><PERSON><PERSON>", "member_benefits_partner_agent_3_value": "Dapatkan hingga 20% cashback dari setiap cashback jar<PERSON><PERSON> bawah", "member_benefits_partner_agent_4": "Cash<PERSON> <PERSON>", "member_benefits_partner_agent_4_value": "Cashback lebih tinggi, hing<PERSON> 100%", "role": "<PERSON><PERSON>", "benefit": "Manfaat", "normal_user": "Pengguna Biasa", "normal_user_2_benefit": "Tidak ada bonus", "agent": "Agen", "member_benefits_silver_agent_2_benefit": "Bisa dapatkan bonus hingga Rp 10.000.000", "normal_user_3_benefit": "Tidak ada komisi belanja", "member_benefits_silver_agent_3_benefit": "Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) ", "normal_user_4_benefit": "Tidak ada komisi belanja", "member_benefits_silver_agent_4_benefit": "Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) ", "normal_user_5_benefit": "Tidak ada komisi belanja", "member_benefits_silver_agent_5_benefit": "Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) ", "order_payment": "<PERSON><PERSON><PERSON><PERSON>", "order_price": "<PERSON><PERSON><PERSON>", "product_name": "<PERSON><PERSON>", "real_payment_price": "<PERSON><PERSON><PERSON> Aktual", "agent_fee": "Biaya Agen", "purchase_right_now": "<PERSON><PERSON>", "payment_problem": "<PERSON><PERSON><PERSON>", "payment_complete": "Pembayaran <PERSON>", "payment_agreement": "Baca dan Set<PERSON>", "payment_agreement_link": "<PERSON><PERSON><PERSON>", "cashback_is_0": "Produk ini tidak ada cashback", "cashback_is_0_content": "Produk ini tidak ada cashback. <PERSON><PERSON><PERSON><PERSON> Anda ingin melanju<PERSON>kan?", "nickname_too_long": "<PERSON><PERSON> pangg<PERSON>n terlalu panjang, maksimal 10 karakter", "check_payment_result": "Memeriksa <PERSON>", "payment_amount": "<PERSON><PERSON><PERSON>", "payment_id": "<PERSON>mor <PERSON>", "payment_method": "<PERSON><PERSON>", "payment_success": "Pembay<PERSON>", "payment_failed": "Pembayaran Gagal", "level_status": "Status Level", "valid_for": "Berlaku: 1 tahun", "upgrade_date": "Tanggal Upgrade: 11 Juni 2025", "to_gold_progress": "Progres Menjadi Agen <PERSON>", "invite_to_upgrade": "Undang 10 teman untuk menjadi Agen Perak atau lebih tinggi", "silver_agent": "<PERSON><PERSON>", "gold_agent": "<PERSON><PERSON>", "diamond_agent": "<PERSON><PERSON>", "partner": "<PERSON><PERSON>", "direct_invite_reward": "<PERSON><PERSON>", "direct_invite_detail": "Undang 1 agen dan dapa<PERSON><PERSON> hadiah Rp 35.000. Undang 3 orang untuk balik modal!", "team_purchase_bonus": "<PERSON> <PERSON><PERSON><PERSON>", "team_purchase_detail": "Dapatkan keuntungan 10% dari total belanja tim yang kamu undang langsung", "team_purchase_detail_gold": "Undangan tidak langsung (tingkat dua): masing-masing dapat terkumpul Rp 15.000; Undangan tidak langsung (tingkat tiga): masing-masing dapat terkumpul Rp 10.000.", "training": "<PERSON><PERSON><PERSON><PERSON>", "training_detail": "Mentor profesional memberikan kursus berkualitas tinggi dan layanan bimbingan menye<PERSON><PERSON>h", "extra_cashback": "Cashback Ekstra", "extra_cashback_detail_gold": "Tiap kembangkan 10 Agen Emas: <PERSON><PERSON> Rp 300.000", "extra_cashback_detail": "Nikmati manfaat cashback ekstra selama periode tertentu", "invite_to_upgrade_empty": "Undang 10 teman baru untuk bergabung menjadi Agen Perak atau level lebih tinggi.\\nAnda akan ter-upgrade status secara otomatis!", "silver_partner": "<PERSON><PERSON>", "gold_partner": "<PERSON><PERSON>", "diamond_partner": "<PERSON><PERSON>", "partner_extra_bonus1": "Tiap kembangkan 10 Mitra Perak: Hadiah Rp 1.000.000", "partner_extra_bonus2": "Tiap kembangkan 10 Mitra Emas: Hadiah Rp 2.000.000", "direct_invite_detail2": "Tiap undang 1 agen, hadiah Rp 200.000, undang 3 orang balik modal seketika!", "team_purchase_detail_gold2": "Undangan tidak langsung (tingkat dua): masing-masing dapat Rp 100.000; Undangan tidak langsung (tingkat tiga): masing-masing dapat Rp 50.000", "extra_bonus": "Bonus Ekstra", "normal_member": "Member Biasa", "high_cashback": "Cash<PERSON> <PERSON><PERSON><PERSON>", "high_cashback_description": "Semakin banyak belanja, semakin banyak hemat", "no_limit": "Tanpa Batas", "no_limit_description": "Nikmati cashback tanpa batas", "user_service": "<PERSON><PERSON><PERSON>", "user_service_description": "Layanan pelanggan berkualitas", "invite_and_eran_bonus": "Undang dan <PERSON>", "invite_code": "<PERSON><PERSON>", "input_invite_code": "Masukkan Ko<PERSON> Undangan", "contact_up": "Hubu<PERSON><PERSON>", "congratulation_to_add_group": "<PERSON><PERSON>at Anda bergabung dengan", "group": " tim", "my_team": "<PERSON>", "task_center": "Pusat <PERSON>", "task_center_title": "Pusat <PERSON>", "task_cash_income": "Pendapatan Tunai (Rp)", "task_withdraw": "<PERSON><PERSON>", "task_withdrawable_amount": "<PERSON><PERSON><PERSON> ya<PERSON>", "task_daily_tasks": "<PERSON><PERSON><PERSON>", "task_invite_reward": "<PERSON><PERSON>", "task_invite_progress": "Progres Undangan", "task_order_progress": "<PERSON><PERSON><PERSON>", "task_invite_count": "<PERSON><PERSON><PERSON>", "task_order_count": "<PERSON><PERSON><PERSON>", "task_conditions_met": "<PERSON><PERSON><PERSON>", "task_conditions_not_met": "Kondisi Tidak Terpenuhi", "task_go_claim": "<PERSON><PERSON><PERSON>", "task_feature_developing": "Fitur Sedang Dikembangkan", "task_developing": "Fitur Tugas Sedang Dikembangkan", "task_return_cash_welfare": "Kesejahteraan <PERSON>", "task_return_cash_welfare_desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "task_view_record": "Lihat Catatan", "task_record_title": "Catatan Tugas", "task_total_invites": "Total Undangan", "task_redeemed_invites": "<PERSON><PERSON><PERSON>", "task_total_orders": "Total Pesanan", "task_redeemed_orders": "<PERSON><PERSON><PERSON>", "task_close": "<PERSON><PERSON><PERSON>", "task_reward_amount": "<PERSON><PERSON><PERSON>", "task_per_completion": "<PERSON>", "invite_bonus": "Bonus Undangan", "shopping_bonus": "Bonus Belanja", "cumulative_number_of_invitations": "<PERSON><PERSON><PERSON>dangan Kumulatif", "today": "<PERSON>", "invite_agent": "Undang Agen", "invite_normal_user": "Undang Pengguna Biasa", "silver": "<PERSON><PERSON>", "gold": "<PERSON><PERSON>", "diamond": "<PERSON><PERSON><PERSON>", "team_support": "Kontribusi Tim", "received_bonus": "Bonus yang <PERSON>ima", "invite_time": "<PERSON><PERSON><PERSON>", "pre_team_cashback": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "invite_and_earn_money": "Undang dan <PERSON><PERSON>", "level_up_schedule": "<PERSON><PERSON><PERSON> Naik <PERSON>", "level_up_description": "<PERSON><PERSON><PERSON><PERSON>", "level_up_content_title": "Undang 10 teman men<PERSON><PERSON> atau level lebih tinggi", "level_up_description_title": "IDR Di <PERSON>", "level_up_description_title1": "Cukup kembangkan 10 Agen Berlian!", "invite_gold_agent": "Undang Agen Emas", "invite_diamond_agent": "Undang Agen Berlian", "level_up_bonus": "Progres dan <PERSON><PERSON>", "activity_rule": "Aturan Aktivitas", "bonus": "<PERSON><PERSON>", "direct_invite_detail3": "<PERSON><PERSON><PERSON> agen, hadiah Rp 35.000 / orang", "team_bonus": "Bonus Tim", "team_bonus_detail": "Rekomendasi tidak langsung (tingkat dua): setiap orang terkumpul Rp 100.000;\nRekomendasi tidak langsung (tingkat tiga): setiap orang terkumpul Rp 50.000;\nJika Anda mencapai status “Mitra Emas” (ber<PERSON><PERSON> merekomendasikan langsung 10 mitra), hadiah dapat ditarik, berlaku selama 60 hari sejak hadiah dihasilkan. Jika tidak mencapai dalam waktu tersebut, hadiah akan hangus secara otomatis.", "partner_extra_bonus3": "Tiap kembangkan 10 Mitra Emas: <PERSON><PERSON> Rp 2.000.000;\nTiap kembangkan 10 Mitra Berlian: <PERSON><PERSON> Rp 100.000.000.", "invite_code_empty_hint": "Kode undangan tidak boleh kosong!", "copy_success": "<PERSON><PERSON><PERSON><PERSON>", "network_is_not_available": "<PERSON><PERSON><PERSON> tidak tersedia, silakan periksa koneksi <PERSON>a", "login_with_other_method": "<PERSON><PERSON><PERSON> dengan metode lain", "member_introduction_level_silver_agent": "Silver", "normal_member_user": "Normal", "agree_with_payment_term": "<PERSON><PERSON><PERSON><PERSON> Anda setuju dengan syarat pembayaran?", "please_choose_payment_method": "<PERSON><PERSON>an pilih metode pembayaran", "qrcode": "KODE QR", "open_payment_link": "Buka Tautan Pembayaran Langsung", "pay_with_qrcode": "Bayar dengan QR Code", "pay_with_qrcode_usage": "Scan kode QR untuk membuka tautan pembayaran dan melakukan pembayaran. <PERSON>ka ingin membayar langsung di aplikasi ini, ketuk untuk membuka tautannya.", "jump_link_failed": "Gagal Membuka Tau<PERSON>", "login_expired_hint": "Login telah kedal<PERSON>, silakan login kembali!", "network_error": "<PERSON><PERSON><PERSON>", "unknown_error": "Kesalahan tidak diketahui"}