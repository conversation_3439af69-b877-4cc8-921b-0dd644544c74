package com.genco.service.service.impl;

import cn.hutool.core.util.StrUtil;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.request.AffiliateProductSearchRequest;
import com.genco.common.response.AffiliateProductResponse;
import com.genco.service.service.AffiliateProductService;
import com.genco.service.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202501Api;
import tiktokshop.open.sdk_java.invoke.ApiClient;
import tiktokshop.open.sdk_java.invoke.ApiException;
import tiktokshop.open.sdk_java.invoke.ApiResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 联盟选品服务实现类
 */
@Slf4j
@Service
public class AffiliateProductServiceImpl implements AffiliateProductService {

    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public AffiliateProductResponse searchProducts(AffiliateProductSearchRequest request) throws ApiException {
        log.info("开始联盟选品查询，请求参数: {}", request);

        // 创建API客户端
        ApiClient apiClient = createApiClient();
        AffiliateCreatorV202501Api apiInstance = new AffiliateCreatorV202501Api(apiClient);

        // 构建请求体
        CreatorSelectAffiliateProductRequestBody requestBody = buildRequestBody(request);

        // 调用TikTok API
        log.info("调用TikTok联盟选品API，pageSize: {}, cursor: {}",
                request.getPageSize(), request.getCursor());

        ApiResponse<CreatorSelectAffiliateProductResponse> response = apiInstance
                .affiliateCreator202501SelectionProductsSearchPostWithHttpInfo(
                        apiClient.getTokens(),
                        "application/json",
                        request.getCursor(),
                        request.getPageSize().intValue(),
                        requestBody
                );

        log.info("TikTok API调用成功，响应码: {}, 消息: {}",
                response.getData().getCode(), response.getData().getMessage());

        // 转换响应数据
        return convertToResponse(response.getData());
    }

    /**
     * 创建API客户端
     */
    private ApiClient createApiClient() {
        // 获取配置信息
        String appKey = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_KEY);
        String appSecret = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_SECRET);
        String accessToken = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ACCESS_TOKEN);

        // 验证必要的配置
        if (StrUtil.isBlank(appKey)) {
            throw new CrmebException("TikTok App Key未配置");
        }
        if (StrUtil.isBlank(appSecret)) {
            throw new CrmebException("TikTok App Secret未配置");
        }
        if (StrUtil.isBlank(accessToken)) {
            throw new CrmebException("TikTok Access Token未配置");
        }

        // 创建API客户端
        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(appKey);
        apiClient.setSecret(appSecret);
        apiClient.setTokens(accessToken);
        apiClient.setBasePath("https://open-api.tiktokglobalshop.com");

        return apiClient;
    }

    /**
     * 构建请求体
     */
    private CreatorSelectAffiliateProductRequestBody buildRequestBody(AffiliateProductSearchRequest request) {
        CreatorSelectAffiliateProductRequestBody requestBody =
                new CreatorSelectAffiliateProductRequestBody();

        // 创建过滤参数
        CreatorSelectAffiliateProductRequestBodyFilterParams filterParams =
                new CreatorSelectAffiliateProductRequestBodyFilterParams();

        // 设置标题关键词
        if (request.getTitleKeyword() != null && !request.getTitleKeyword().trim().isEmpty()) {
            filterParams.setTitleKeyword(request.getTitleKeyword().trim());
        }

        // 设置产品ID列表
        if (request.getProductIds() != null && !request.getProductIds().isEmpty()) {
            filterParams.setProductIds(request.getProductIds());
        }

        // 设置分类ID列表
        if (request.getCategoryIds() != null && !request.getCategoryIds().isEmpty()) {
            filterParams.setCategoryIds(request.getCategoryIds());
        }

        // 设置佣金率范围
        if (request.getCommissionRateGe() != null || request.getCommissionRateLe() != null) {
            CreatorSelectAffiliateProductRequestBodyFilterParamsCommissionRateRange commissionRange =
                    new CreatorSelectAffiliateProductRequestBodyFilterParamsCommissionRateRange();
            if (request.getCommissionRateGe() != null) {
                commissionRange.setRateGe(request.getCommissionRateGe());
            }
            if (request.getCommissionRateLe() != null) {
                commissionRange.setRateLe(request.getCommissionRateLe());
            }
            filterParams.setCommissionRateRange(commissionRange);
        }

        // 设置价格范围
        if (request.getPriceGe() != null || request.getPriceLe() != null) {
            CreatorSelectAffiliateProductRequestBodyFilterParamsPriceRange priceRange =
                    new CreatorSelectAffiliateProductRequestBodyFilterParamsPriceRange();
            if (request.getPriceGe() != null) {
                priceRange.setPriceGe(request.getPriceGe().toString());
            }
            if (request.getPriceLe() != null) {
                priceRange.setPriceLe(request.getPriceLe().toString());
            }
            filterParams.setPriceRange(priceRange);
        }

        // 设置销量范围
        if (request.getSoldQuantityGe() != null || request.getSoldQuantityLe() != null) {
            CreatorSelectAffiliateProductRequestBodyFilterParamsSoldQuantityRange soldRange =
                    new CreatorSelectAffiliateProductRequestBodyFilterParamsSoldQuantityRange();
            if (request.getSoldQuantityGe() != null) {
                soldRange.setQuantityGe(request.getSoldQuantityGe());
            }
            if (request.getSoldQuantityLe() != null) {
                soldRange.setQuantityLe(request.getSoldQuantityLe());
            }
            filterParams.setSoldQuantityRange(soldRange);
        }

        // 设置店铺评分范围
        if (request.getShopRatingGe() != null || request.getShopRatingLe() != null) {
            CreatorSelectAffiliateProductRequestBodyFilterParamsShopRatingRange ratingRange =
                    new CreatorSelectAffiliateProductRequestBodyFilterParamsShopRatingRange();
            if (request.getShopRatingGe() != null) {
                ratingRange.setRatingGe(request.getShopRatingGe());
            }
            if (request.getShopRatingLe() != null) {
                ratingRange.setRatingLe(request.getShopRatingLe());
            }
            filterParams.setShopRatingRange(ratingRange);
        }

        // 设置产品池ID
        if (request.getPoolIds() != null && !request.getPoolIds().isEmpty()) {
            filterParams.setPoolIds(request.getPoolIds());
        }

        requestBody.setFilterParams(filterParams);

        // 设置排序参数
        CreatorSelectAffiliateProductRequestBodySortParams sortParams =
                new CreatorSelectAffiliateProductRequestBodySortParams();

        String sortType = request.getSortType();
        if (sortType == null || sortType.trim().isEmpty()) {
            sortType = "RECOMMENDED"; // 默认推荐排序
        } else {
            // 确保为大写格式
            sortType = sortType.toUpperCase();
        }

        sortParams.setSortType(sortType);
        requestBody.setSortParams(sortParams);

        return requestBody;
    }

    /**
     * 转换响应数据
     */
    private AffiliateProductResponse convertToResponse(CreatorSelectAffiliateProductResponse response) {
        AffiliateProductResponse result = new AffiliateProductResponse();

        if (response.getData() != null) {
            CreatorSelectAffiliateProductResponseData data = response.getData();

            result.setNextPageToken(data.getNextPageToken());
            // 安全转换Integer到Long
            if (data.getTotalCount() != null) {
                result.setTotalCount(data.getTotalCount().longValue());
            }

            // 转换产品列表
            if (data.getProducts() != null) {
                List<AffiliateProductResponse.AffiliateProduct> products = data.getProducts().stream()
                        .map(this::convertProduct)
                        .collect(Collectors.toList());
                result.setProducts(products);
            } else {
                result.setProducts(new ArrayList<>());
            }
        }

        return result;
    }

    /**
     * 转换单个产品数据
     */
    private AffiliateProductResponse.AffiliateProduct convertProduct(
            CreatorSelectAffiliateProductResponseDataProducts product) {

        AffiliateProductResponse.AffiliateProduct result = new AffiliateProductResponse.AffiliateProduct();

        result.setId(product.getId());
        result.setTitle(product.getTitle());
        result.setMainImageUrl(product.getMainImageUrl());

        // V202501没有detailLink字段，设置为空
        result.setDetailLink("");

        // 根据库存数量判断是否有库存
        if (product.getStock() != null && product.getStock().getQuantity() != null) {
            result.setHasInventory(product.getStock().getQuantity() > 0);
        } else {
            result.setHasInventory(false);
        }

        // 从marketPerformance获取销量
        if (product.getMarketPerformance() != null &&
            product.getMarketPerformance().getHistoricalSoldQuantity() != null) {
            result.setUnitsSold(product.getMarketPerformance().getHistoricalSoldQuantity());
        } else {
            result.setUnitsSold(0);
        }

        // V202501没有saleRegion字段，设置为空
        result.setSaleRegion("");

        // V202501只有price字段，没有originalPrice和salesPrice的区分
        if (product.getPrice() != null) {
            AffiliateProductResponse.PriceInfo priceInfo = convertV202501PriceInfo(product.getPrice());
            result.setSalesPrice(priceInfo);
            // 原价设置为相同值
            result.setOriginalPrice(priceInfo);
        }

        // 转换佣金信息
        if (product.getCommission() != null) {
            result.setCommission(convertV202501CommissionInfo(product.getCommission()));
        }

        // 转换店铺信息
        if (product.getShop() != null) {
            AffiliateProductResponse.ShopInfo shopInfo = new AffiliateProductResponse.ShopInfo();
            shopInfo.setName(product.getShop().getName());
            result.setShop(shopInfo);
        }

        // 转换评价信息
        if (product.getReview() != null) {
            AffiliateProductResponse.ReviewInfo reviewInfo = new AffiliateProductResponse.ReviewInfo();
            reviewInfo.setCount(product.getReview().getCount());
            reviewInfo.setOverallScore(product.getReview().getOverallScore());
            result.setReview(reviewInfo);
        }

        return result;
    }

    /**
     * 转换V202501价格信息
     */
    private AffiliateProductResponse.PriceInfo convertV202501PriceInfo(
            CreatorSelectAffiliateProductResponseDataProductsPrice price) {

        AffiliateProductResponse.PriceInfo priceInfo = new AffiliateProductResponse.PriceInfo();
        priceInfo.setMinimumAmount(price.getFloorPrice());
        priceInfo.setMaximumAmount(price.getCeilingPrice());
        priceInfo.setCurrency(price.getCurrency());

        return priceInfo;
    }

    /**
     * 转换V202501佣金信息
     */
    private AffiliateProductResponse.CommissionInfo convertV202501CommissionInfo(
            CreatorSelectAffiliateProductResponseDataProductsCommission commission) {

        AffiliateProductResponse.CommissionInfo commissionInfo = new AffiliateProductResponse.CommissionInfo();
        // 安全转换Integer到Long
        if (commission.getRate() != null) {
            commissionInfo.setRate(commission.getRate().longValue());
        }
        commissionInfo.setAmount(commission.getAmount());
        // V202501的佣金信息中没有currency字段，设置为空或默认值
        commissionInfo.setCurrency("IDR");

        return commissionInfo;
    }

    /**
     * 掩码敏感信息
     */
    private String maskSensitiveInfo(String info) {
        if (StrUtil.isBlank(info) || info.length() <= 8) {
            return "****";
        }
        return info.substring(0, 4) + "****" + info.substring(info.length() - 4);
    }
}
