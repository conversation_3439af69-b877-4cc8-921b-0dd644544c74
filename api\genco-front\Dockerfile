FROM openjdk:17
LABEL maintainer="<EMAIL>"

# 设置时区为印尼时间 (WIB - UTC+7)
ENV TZ=Asia/Jakarta
RUN microdnf update -y && microdnf install -y tzdata && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone && \
    microdnf clean all

WORKDIR /app
COPY target/Genco-front.jar /app/Genco-front.jar
ENTRYPOINT ["java", "-Duser.timezone=Asia/Jakarta", "-Dfile.encoding=UTF-8", "-jar", "/app/Genco-front.jar", "--spring.profiles.active=prod"]  