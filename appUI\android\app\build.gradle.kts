plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.genconusantara.milestone"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    // keytool -list -v -keystore milestone.keystore -alias com.bravedefault.milestone -storepass milestone -keypass milestone
    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.genconusantara.milestone"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = 30
        versionName = "1.0.3"
    }

    signingConfigs {
        create("release") {
            keyAlias = "com.bravedefault.milestone"
            keyPassword = "milestone"
            storeFile = file("milestone.keystore")
            storePassword = "milestone"
        }
    }

    buildTypes {
        debug {
            signingConfig = signingConfigs.getByName("release")
        }
        release {
            signingConfig = signingConfigs.getByName("release")
        }
    }
}

dependencies {
    implementation("com.tiktok.open.sdk:tiktok-open-sdk-core:2.3.0")
    implementation("com.tiktok.open.sdk:tiktok-open-sdk-auth:2.3.0")   // to use authorization api
    implementation("com.tiktok.open.sdk:tiktok-open-sdk-share:2.3.0")    // to use share api
}


flutter {
    source = "../.."
}
