{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-85.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "analyzer", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/analyzer-7.7.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "animated_flip_counter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/animated_flip_counter-0.3.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "archive", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/archive-4.0.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "azlistview", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/azlistview-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "build", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build-3.0.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_config-1.1.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_daemon", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_daemon-4.0.4", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_resolvers", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_resolvers-3.0.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_runner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_runner-2.6.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "build_runner_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_runner_core-9.2.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "built_collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_value-8.11.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cached_network_image-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cached_network_image_web-1.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "carousel_slider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/carousel_slider-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "characters", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "checked_yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.4", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "clock", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "code_builder", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/code_builder-4.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "connectivity_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "connectivity_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/connectivity_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "convert", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "crop_image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/crop_image-1.0.16", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "cross_file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dart_style", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dart_style-3.1.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "dbus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "device_info_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/device_info_plus_platform_interface-7.0.3", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "dio", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio-5.8.0+1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio_smart_retry", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio_smart_retry-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dio_web_adapter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio_web_adapter-2.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "dotted_border", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dotted_border-3.1.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "equatable", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/equatable-2.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "event_bus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/event_bus-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "extended_image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/extended_image-10.0.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "extended_image_library", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/extended_image_library-5.0.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "extended_list", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/extended_list-3.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "extended_list_library", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/extended_list_library-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "extended_nested_scroll_view", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/extended_nested_scroll_view-6.2.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "fake_async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "faker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/faker-2.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "ffi", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_macos-0.9.4+3", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "file_selector_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///C:/flutter/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_cache_manager", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_easyloading", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_easyloading-3.0.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_highlight", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_highlight-0.7.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_lints-6.0.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "flutter_localizations", "rootUri": "file:///C:/flutter/flutter/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_plugin_android_lifecycle-2.0.28", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_spinkit", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_spinkit-5.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_staggered_grid_view", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_staggered_grid_view-0.7.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_test", "rootUri": "file:///C:/flutter/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///C:/flutter/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "font_awesome_flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/font_awesome_flutter-10.8.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "frontend_server_client", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "get", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/get-4.7.2", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "glob", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "graphs", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/graphs-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "highlight", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/highlight-0.7.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_client_helper", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_client_helper-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "http_multi_server", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "iconsax_flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/iconsax_flutter-1.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "image_picker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_android-0.8.12+24", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "image_picker_for_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_linux-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_macos-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_platform_interface-2.10.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "intl", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/intl-0.20.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "intl_utils", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/intl_utils-2.8.11", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "io", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/io-1.0.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "js", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/js-0.7.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "json_annotation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "json_serializable", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/json_serializable-6.10.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "leak_tracker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/lints-6.0.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "loading_more_list", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/loading_more_list-7.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "loading_more_list_library", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/loading_more_list_library-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "logger", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/logger-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "logging", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "markdown", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/markdown-7.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "markdown_widget", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/markdown_widget-2.3.2+8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "matcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nested", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nm", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/nm-0.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "octo_image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "package_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "package_info_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_drawing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_drawing-1.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path_parsing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pausable_timer", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pausable_timer-3.1.0+3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "petitparser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "platform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pool", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "posix", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/posix-6.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pub_semver", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pubspec_parse", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pubspec_parse-1.5.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "qr", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/qr-3.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "qr_flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/qr_flutter-4.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "rxdart", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/rxdart-0.28.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "scroll_to_index", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/scroll_to_index-3.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "scrollable_positioned_list", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/scrollable_positioned_list-0.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "share_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/share_plus-11.0.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "share_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/share_plus_platform_interface-6.0.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "shared_preferences", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf-1.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shelf_web_socket", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_web_socket-3.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "skeletonizer", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/skeletonizer-2.1.0+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "sky_engine", "rootUri": "file:///C:/flutter/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_gen", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_gen-3.0.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_helper", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_helper-1.3.6", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/sqflite_common-2.5.6", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "sqflite_darwin", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "step_progress", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/step_progress-2.6.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "stream_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "synchronized", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/synchronized-3.4.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "tiktok_sdk_v2", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/tiktok_sdk_v2-2cd7d7317b00aeb67ffa42466ce644479425b86b/", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "timezone", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/timezone-0.10.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "timing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/timing-1.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "toastification", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/toastification-3.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "typed_data", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher-6.3.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.16", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_math", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "visibility_detector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/visibility_detector-0.4.0+2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "vm_service", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "watcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/watcher-1.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "waterfall_flow", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/waterfall_flow-3.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web_socket-1.0.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web_socket_channel-3.0.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "win32", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/win32-5.14.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "win32_registry", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/win32_registry-2.1.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "xdg_directories", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "milestone", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.8"}], "generator": "pub", "generatorVersion": "3.8.1", "flutterRoot": "file:///C:/flutter/flutter", "flutterVersion": "3.32.8", "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"}