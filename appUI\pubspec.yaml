name: milestone
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.3+28

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  dio: ^5.8.0+1
  dio_smart_retry: ^7.0.1
  flutter_staggered_grid_view: ^0.7.0
  shared_preferences: ^2.5.3
  cached_network_image: ^3.4.1
  device_info_plus: ^11.5.0
  extended_nested_scroll_view: ^6.2.1
  loading_more_list: ^7.1.0
  synchronized: ^3.3.1
  logger: ^2.5.0
  carousel_slider: ^5.1.1
  provider: ^6.1.5
  intl: ^0.20.2
  flutter_easyloading: ^3.0.5
  faker: ^2.2.0
  font_awesome_flutter: ^10.8.0
  json_annotation: ^4.9.0
  uuid: ^4.5.1
  azlistview: ^2.0.0
  step_progress: ^2.5.1
  image_picker: ^1.1.2
  extended_image: ^10.0.1
  package_info_plus: ^8.3.0
  markdown_widget: ^2.3.2+8
  tiktok_sdk_v2:
    git:
      url: https://github.com/zzycami/tiktok_sdk_v2.git
  toastification: ^3.0.3
  skeletonizer: ^2.0.1
  url_launcher: ^6.3.1
  share_plus: ^11.0.0
  crop_image: ^1.0.16
  event_bus: ^2.0.1
  get: ^4.7.2
  path_drawing: ^1.0.1
  http: ^1.4.0
  animated_flip_counter: ^0.3.4
  qr_flutter: ^4.1.0
  path_provider: ^2.1.0
  connectivity_plus: ^6.1.4
#  flutter_local_notifications: ^19.1.0
  timezone: ^0.10.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  intl_utils: ^2.8.10
  build_runner: ^2.4.15
  json_serializable: ^6.9.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/data/
    - assets/data/faq_zh.json
    - assets/data/faq_en.json
    - assets/data/faq_id.json
    - assets/data/privacy.txt
    - assets/data/term_of_service.txt
    - assets/data/term_of_payment.txt
    - assets/data/about_zh.md
    - assets/data/about_en.md
    - assets/data/about_id.md
    - assets/images/
    # assets-generator-begin
    # assets/images/*
    - assets/images/ic_me_setting_avatar_placeholder.png
    - assets/images/ic_detail_question.png
    - assets/images/ic_customer_service.png
    - assets/images/img_guide_step_2_example.png
    - assets/images/img_usage_guideline_step_1.png
    - assets/images/ic_home_high_rebate.png
    - assets/images/ic_member_level_right.png
    - assets/images/bg_me_invite.png
    - assets/images/ic_income_withdrawal.png
    - assets/images/ic_income_tab.png
    - assets/images/bg_member_partner_silver.png
    - assets/images/bg_member_level_tab_sliver_agent.png
    - assets/images/bg_rebase_cashback_flow_1.png
    - assets/images/bg_brand_product_list.png
    - assets/images/ic_brand_good_amount.png
    - assets/images/ic_payment_order.png
    - assets/images/ic_me_arrow_left.png
    - assets/images/ic_home_tab.png
    - assets/images/ic_normal_user.png
    - assets/images/ic_member_level_benefit_4.png
    - assets/images/ic_go_pay.png
    - assets/images/img_usage_guideline_step_2.png
    - assets/images/ic_default_avatar.png
    - assets/images/ic_brand_down.png
    - assets/images/img_usage_guideline_step_3.png
    - assets/images/bg_guide_step_1.png
    - assets/images/ic_member_level_badge.png
    - assets/images/ic_income_arrow_right.png
    - assets/images/ic_shopee_pay.png
    - assets/images/bg_me_usage.png
    - assets/images/ic_close_white.png
    - assets/images/bg_rebase_cashback_flow_2.png
    - assets/images/ic_avatar_default.png
    - assets/images/ic_silver_agent.png
    - assets/images/bg_credited_top.png
    - assets/images/bg_withdrawal_success.png
    - assets/images/bg_home_cashback_flow.png
    - assets/images/ic_rebase_check.png
    - assets/images/ic_member_level_benefit_1.png
    - assets/images/ic_withdrawal_success.png
    - assets/images/bg_partners_agen_emas.png
    - assets/images/ic_home_question.png
    - assets/images/bg_income_totoal_income.png
    - assets/images/ic_member_cash_back_no_limit.png
    - assets/images/ic_detail_cashback_flow_2_tiktok.png
    - assets/images/ic_detail_arrow_right.png
    - assets/images/bg_income_top.png
    - assets/images/ic_member_level_benefit_2.png
    - assets/images/ic_delete_account.png
    - assets/images/ic_home_shopee_12.png
    - assets/images/bg_rebase_hint_top.png
    - assets/images/ic_user_avatar.png
    - assets/images/ic_member_level_benefit_3.png
    - assets/images/bg_member_intro.png
    - assets/images/ic_transaction_detail_all.png
    - assets/images/ic_money_bank.png
    - assets/images/ic_me_step_circle.png
    - assets/images/ic_detail_back_arrow.png
    - assets/images/ic_tiktok_20.png
    - assets/images/ic_home_tiktok_14.png
    - assets/images/ic_member_extra_cashback.png
    - assets/images/brand_example_01.png
    - assets/images/ic_income_my_order.png
    - assets/images/ic_favorite_button.png
    - assets/images/bg_home_cashback_flow_step.png
    - assets/images/ic_logo_14.png
    - assets/images/bg_income_dialog.png
    - assets/images/bg_member_tab_left.png
    - assets/images/img_guide_step_4_example.png
    - assets/images/bg_level_up_description.png
    - assets/images/error_loading.png
    - assets/images/bg_member_vip.png
    - assets/images/ic_indonesia.png
    - assets/images/bg_group_team_select.png
    - assets/images/bg_agen_berlian.png
    - assets/images/ic_transaction_detail_income_selected.png
    - assets/images/brand_example_large.png
    - assets/images/bg_member_level_top_black.png
    - assets/images/bg_payment_order.png
    - assets/images/ic_rebase_hint_top.png
    - assets/images/ic_member_duration.png
    - assets/images/bg_detail_cashback.png
    - assets/images/ic_Income_detail.png
    - assets/images/bg_common.png
    - assets/images/bg_home_popover.png
    - assets/images/ic_transaction_detail_expenditure_selected.png
    - assets/images/ic_invite_left.png
    - assets/images/ic_me_decoration_right.png
    - assets/images/img_empty.png
    - assets/images/ic_transaction_detail_all_selected.png
    - assets/images/ic_payment_failed.png
    - assets/images/ic_me_setting_change_password.png
    - assets/images/ic_home_tiktok_12.png
    - assets/images/ic_faq_arrow_top.png
    - assets/images/bg_usage_guideline_step_2.png
    - assets/images/ic_order_point.png
    - assets/images/ic_member_diamond.png
    - assets/images/bg_usage_guideline_step_3.png
    - assets/images/tiktok_48.png
    - assets/images/ic_eye_open.png
    - assets/images/ic_withdraw_add_card.png
    - assets/images/ic_home_logo.png
    - assets/images/ic_arrow_back.png
    - assets/images/ic_transaction_detail_income.png
    - assets/images/bg_about.png
    - assets/images/bg_transaction_detail.png
    - assets/images/ic_brand_tab_selected.png
    - assets/images/ic_guide_step_6_play.png
    - assets/images/bg_brand_home_top.png
    - assets/images/ic_expenditure_47.png
    - assets/images/bg_usage_guideline_step_1.png
    - assets/images/bg_group_team.png
    - assets/images/ic_favrite_star_fill.png
    - assets/images/ic_close_button.png
    - assets/images/bg_partners_agen_berlian.png
    - assets/images/ic_member_level_left.png
    - assets/images/ic_withdraw_add.png
    - assets/images/bg_member_agent_silver.png
    - assets/images/bg_level_up_target.png
    - assets/images/placeholder.png
    - assets/images/bg_privacy.png
    - assets/images/ic_home_cashback_flow_step_4.png
    - assets/images/ic_me_tab.png
    - assets/images/ic_income_tab_selected.png
    - assets/images/ic_me_decoration_left.png
    - assets/images/bg_agen_emas.png
    - assets/images/img_guide_step_6_example.png
    - assets/images/bg_member_level_status.png
    - assets/images/ic_home_search.png
    - assets/images/ic_member_group_bonus.png
    - assets/images/ic_tiktok_14.png
    - assets/images/ic_favrite_star.png
    - assets/images/bg_member_level_top.png
    - assets/images/ic_transaction_detail_expenditure.png
    - assets/images/ic_checkbox_check.png
    - assets/images/bg_member_partner_gold.png
    - assets/images/bg_member_normal.png
    - assets/images/ic_me_star.png
    - assets/images/ic_detail_share.png
    - assets/images/ic_guide_step_1.png
    - assets/images/ic_home_cashback_flow_arrow.png
    - assets/images/ic_eye_close.png
    - assets/images/bg_order.png
    - assets/images/ic_member_level_step.png
    - assets/images/ic_config.png
    - assets/images/bg_member_agent_diamand.png
    - assets/images/ic_search.png
    - assets/images/ic_me_tab_selected.png
    - assets/images/ic_home_cashback_flow_step_2.png
    - assets/images/ic_group_invite.png
    - assets/images/bg_home_top.png
    - assets/images/ic_payment_success.png
    - assets/images/bg_detail_brand_product_cashback_button.png
    - assets/images/ic_call_phone.png
    - assets/images/ic_present.png
    - assets/images/ic_me_setting_phone.png
    - assets/images/ic_home_cashback_flow_step_3.png
    - assets/images/ic_rebase_error.png
    - assets/images/ic_guide_step_decoration.png
    - assets/images/ic_me_invite_code.png
    - assets/images/bg_guide_step_mark.png
    - assets/images/ic_home_cashback_flow_step_1.png
    - assets/images/bg_login.png
    - assets/images/ic_me_setting_faq.png
    - assets/images/bg_member_tab_right.png
    - assets/images/ic_me_step_circle_check.png
    - assets/images/ic_member_bonus.png
    - assets/images/img_guide_step_3_example.png
    - assets/images/ic_diamond_agent.png
    - assets/images/bg_member_off.png
    - assets/images/ic_detail_cashback_flow_point.png
    - assets/images/ic_me_orders.png
    - assets/images/ic_brand_home_tiktok_20.png
    - assets/images/ic_detail_cashback_flow_2.png
    - assets/images/ic_income_47.png
    - assets/images/ic_faq_answer.png
    - assets/images/ic_credited_rebase.png
    - assets/images/ic_my_team.png
    - assets/images/ic_arrow_top.png
    - assets/images/ic_ovo_pay.png
    - assets/images/ic_me_setting_about.png
    - assets/images/bg_home_cashback_button.png
    - assets/images/ic_dana_pay.png
    - assets/images/bg_cash_info.png
    - assets/images/ic_detail_cashback_flow_3.png
    - assets/images/logo_48.png
    - assets/images/ic_faq_question.png
    - assets/images/ic_member_silver_agent.png
    - assets/images/bg_home_cover.png
    - assets/images/ic_brand_tab.png
    - assets/images/bg_agent_perak.png
    - assets/images/bg_member_level_tab_partner.png
    - assets/images/icon_tiktok.png
    - assets/images/ic_password_eye_open.png
    - assets/images/ic_detail_cashback_flow_1.png
    - assets/images/bg_member_partner_diamand.png
    - assets/images/ic_home_tab_selected.png
    - assets/images/bg_member_level_tab_normal.png
    - assets/images/ic_shopee_14.png
    - assets/images/ic_home_cashback_flow_step_point_4.png
    - assets/images/ic_home_tab_indicator.png
    - assets/images/ic_withdraw_e_wallet.png
    - assets/images/ic_detail_cashback_flow_4.png
    - assets/images/bg_member_agent_gold.png
    - assets/images/bg_usage_top.png
    - assets/images/ic_home_cashback_flow_step_point_1.png
    - assets/images/ic_gold_agent.png
    - assets/images/ic_member_partners.png
    - assets/images/ic_qrcode.png
    - assets/images/bg_share_poster.png
    - assets/images/logo_70.png
    - assets/images/ic_me_setting_nickname.png
    - assets/images/ic_me_setting_modify_phone.png
    - assets/images/bg_share_poster_cover.png
    - assets/images/ic_home_cashback_flow_step_point_3.png
    - assets/images/ic_brand_home_star.png
    - assets/images/bg_partners_agen_perak.png
    - assets/images/img_guide_step_5_example.png
    - assets/images/ic_astrapay.png
    - assets/images/ic_me_setting_privacy.png
    - assets/images/ic_home_cashback_flow_step_point_2.png
    - assets/images/ic_member_arrow_right.png
    # assets-generator-end

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
flutter_intl:
  enabled: true
