<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <div class="container mt-1">
          <el-form inline size="small">
            <el-form-item :label="$t('product.search')">
              <el-input
                v-model="form.keywords"
                :placeholder="$t('product.enterProductName')"
                class="selWidth"
                size="small"
                clearable
              />
            </el-form-item>
            <el-form-item :label="$t('product.status')">
              <el-select
                v-model="form.type"
                :placeholder="$t('product.pleaseSelect')"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="$t('product.' + item.label)"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('brand.brandName')">
              <el-select
                v-model="form.brand"
                :placeholder="$t('product.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="item in brandOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <el-button size="small" type="primary" class="mr10" @click="onSearch">
          {{ $t("product.query") }}
        </el-button>
        <el-button size="small" type="" class="mr10" @click="onReset">
          {{ $t("product.reset") }}
        </el-button>

        <div class="acea-row padtop-10">
          <el-button size="small" type="success" @click="onAdd">
            {{ $t("product.addProduct") }}
          </el-button>
          <el-button size="small" @click="batchHandle('online')">
            {{ $t("product.batchOnline") }}
          </el-button>
          <el-button size="small" @click="batchHandle('outline')">
            {{ $t("product.batchOffline") }}
          </el-button>
          <el-button size="small" @click="batchHandle('delete')">
            {{ $t("product.batchDelete") }}
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        style="width: 100%"
        size="mini"
        :highlight-current-row="true"
        :header-cell-style="{ fontWeight: 'bold' }"
        @selection-change="handleSelection"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column :label="$t('product.productImage')" min-width="80">
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image
                style="width: 36px; height: 36px"
                :src="scope.row.image || ''"
                :preview-src-list="[scope.row.image || '']"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('product.productName')"
          min-width="160"
          :show-overflow-tooltip="true"
          prop="storeName"
        />
        <el-table-column
          :label="$t('product.productPrice')"
          min-width="90"
          align="center"
        >
        <template slot-scope="scope">
            {{formatAmount(scope.row.price)}}
        </template>
        
        </el-table-column>
        <el-table-column
          :label="$t('product.cashbackRate')"
          min-width="100"
          align="center"
        >
          <template slot-scope="scope">{{
            formatRate(scope.row.cashBackRate)
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('product.usercashbackRate')"
          min-width="100"
          align="center"
        >
          <template slot-scope="scope">{{
            formatRate(scope.row.userCashBackRate)
          }}</template>
        </el-table-column>

        <el-table-column
          :label="$t('product.estimatedCashback')"
          width="150"
          align="center"
        >
        <template slot-scope="scope">{{
            formatAmount(scope.row.cashBackAmount)
          }}</template>
        </el-table-column>

        <el-table-column
          :label="$t('product.isHot')"
          min-width="80"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isHot"
              :active-value="true"
              :inactive-value="false"
              @change="isShowChange(scope.row, scope.row.isHot, 'isHot')"
            />
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('product.isBenefit')"
          min-width="80"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isBenefit"
              :active-value="true"
              :inactive-value="false"
              @change="
                isShowChange(scope.row, scope.row.isBenefit, 'isBenefit')
              "
            />
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('product.isTikTok')"
          min-width="80"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isBest"
              :active-value="true"
              :inactive-value="false"
              @change="isShowChange(scope.row, scope.row.isBest, 'isBest')"
            />
          </template>
        </el-table-column>

       

        <el-table-column
          :label="$t('product.addTime')"
          min-width="120"
          align="center"
        >
          <template slot-scope="scope">{{
            formatTime(scope.row.addTime)
          }}</template>
        </el-table-column>


         <el-table-column
          :label="$t('product.online')"
          min-width="80"
          align="center"
        >
            <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.isShow"
                  :active-value="true"
                  :inactive-value="false"
                  @change="handleUpdate(scope.row)"
                />
            </template>
        </el-table-column>

        <el-table-column
          :label="$t('product.action')"
          min-width="100"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <!-- <el-button
              type="text"
              size="small"
              class="mr10"
              @click="handleUpdate(scope.row, scope.$index)"
            >
              <span v-if="scope.row.isShow">{{ $t("product.offline") }}</span>
              <span v-else>{{ $t("product.online") }}</span>
            </el-button>
 -->
            <el-button
              type="text"
              size="small"
              class="mr10"
              @click="editProduct(scope.row, scope.$index)"
            >
              {{ $t("product.edit") }}
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row, scope.$index)"
            >
              {{ $t("product.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="form.limit"
          :current-page="form.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>

    <el-dialog
      :title="$t('product.addDialogTitle')"
      :visible.sync="productDialogVisible"
      width="540px"
      :before-close="handleCloseProductDialog"
    >
      <el-form
        class="mt24"
        ref="dform"
        :model="dform"
        label-width="160px"
        @submit.native.prevent
        v-loading="loading"
      >
        <el-form-item :label="$t('product.enterProductLink')">
          <el-input
            v-model="url"
            placeholder="please input link"
            class="selWidth width200"
            size="small"
            clearable
          />
          <el-button size="small" @click="fetchProduct">
            {{ $t("product.fetchProductInfo") }}
          </el-button>
        </el-form-item>

        <el-form-item :label="$t('brand.brandName')">
          <el-select
            v-model="brandName"
            :placeholder="$t('brand.pleaseSelect')"
          >
            <el-option
              v-for="item in brandOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('product.productName')">
          <el-input
            readonly
            v-model="dform.storeName"
            :placeholder="$t('product.enterProductName')"
            class="selWidth readonly-input"
            size="small"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('product.productImage')">
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image
                style="width: 36px; height: 36px"
                :src="dform.image"
                :preview-src-list="[dform.image]"
              />
            </div>
          </template>
        </el-form-item>
        <el-form-item :label="$t('product.productPrice')">
          <el-input
            readonly
            v-model="dform.price"
            :placeholder="$t('product.enterProductPrice')"
            class="selWidth readonly-input"
            size="small"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('product.cashbackRate')">
          <el-input
            readonly
            v-model="dform.forMatUserCashBackRate"
            :placeholder="$t('product.enterCashbackRate')"
            class="selWidth readonly-input"
            size="small"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('product.usercashbackRate')">
          <el-input
            readonly
            v-model="dform.forMatCashBackRate"
            :placeholder="$t('product.enterCashbackRate')"
            class="selWidth readonly-input"
            size="small"
            clearable
          />
        </el-form-item>

        <el-form-item :label="$t('product.estimatedCashback')">
          <el-input
            readonly
            v-model="dform.cashBackAmount"
            :placeholder="$t('product.enterCashbackRate')"
            class="selWidth readonly-input"
            size="small"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('product.isOnline')">
          <el-select v-model="status" :placeholder="$t('product.pleaseSelect')">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="$t('product.' + item.label)"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onSubProduct">{{
          $t("product.confirm")
        }}</el-button>
        <el-button @click="handleCloseProductDialog">{{
          $t("product.cancel")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  brandLstApi,
  productListApi,
  fetchProductApi,
  batchPutOn,
  batchPutoff,
  batchDelete,
  updateProductInfo,
  newProduct
} from "@/api/brand";
import { getToken } from "@/utils/auth";
import { checkPermi } from "@/utils/permission";

export default {
  name: "BrandProductList",
  data() {
    return {
      statusOptions: [
        { value: -1, label: this.$t("all") },
        { value: 1, label: this.$t("online") },
        { value: 2, label: this.$t("offline") }
      ],
      typeOptions: [
        { value: "1", label: this.$t("yes") },
        { value: "0", label: this.$t("no") }
      ],
      loading: false,
      listLoading: false,
      tableData: {
        data: [],
        total: 0
      },
      form: {
        page: 1,
        limit: 20,
        keywords: "",
        type: -1,
        total: 0,
        isIndex: false,
        brand: ""
      },
      url: "",
      brandName: "",
      // dform: {
      //   id: '',
      //   url: '',
      //   storeName: '',
      //   image: '',
      //   price: '',
      //   cashbackRate: '',
      //   // cashbackAmount: '',
      //   status: '',
      // },
      dform: {},
      status: "",
      productDialogVisible: false,
      multipleSelection: [],
      brandOptions: []
    };
  },
  mounted() {
    var brand = this.$route.query.brand || "";
    this.form.brand = brand;

    this.getList();
    this.getBrands();
  },
  watch: {
    "$route.query.brand"(newBrand) {
      this.form.brand = newBrand;
      this.getList();
    }
  },
  methods: {
    checkPermi,
    formatAmount(s){
        if(s == undefined) {
            s = 0
        }
        let s1 = (s/1000).toFixed(3)
        return s1
    },
    getBrands() {
      brandLstApi({ page: 1, limit: 10000, type: "-1", name: "" })
        .then(res => {
          if (res.list) {
            let options = [];
            for (var i in res.list) {
              var itm = res.list[i];
              options.push({
                label: itm["name"],
                value: itm["code"]
              });
            }
            this.brandOptions = options;
            console.log(this.brandOptions);
          }
          // this.tableData.data = res.list;
          // this.tableData.total = res.total;
          // this.listLoading = false;
        })
        .catch(res => {
          // this.listLoading = false;
          // this.$message.error(this.$t("common.fetchDataFailed"));
        });
    },
    getList() {
      let _this = this;
      this.listLoading = true;
      if (this.form.type == -1) {
        delete this.form.type;
      }
      productListApi(this.form)
        .then(res => {
          _this.listLoading = false;
          _this.tableData.data = res.list;
          _this.tableData.total = res.total;
        })
        .catch(res => {
          this.listLoading = false;
          this.$message.error(this.$t("common.fetchDataFailed"));
        });
    },
    onSearch() {
      this.form.page = 1;
      this.getList();
    },
    onReset() {
      this.form.keywords = "";
      this.form.status = "-1";
    },
    onAdd() {
      this.productDialogVisible = true;
    },
    handleSelection(val) {
      this.multipleSelection = val;
    },
    batchHandle(type) {
      let _this = this;
      let rows = [];
      this.multipleSelection.forEach(row => {
        rows.push(row.id);
      });
      if (rows.length > 0) {
        this.listLoading = true;
        let params = { ids: rows };
        if (type === "online") {
          batchPutOn(params).then(res => {
            _this.getList();
          });
        } else if (type === "outline") {
          batchPutoff(params).then(res => {
            _this.getList();
          });
        } else if (type === "delete") {
          params.type = "recycle";
          batchDelete(params).then(res => {
            _this.getList();
          });
        }
      }
    },
    handleSizeChange(val) {
      this.form.limit = val;
      this.getList();
    },
    pageChange(page) {
      this.form.page = page;
      this.getList();
    },
    handleUpdate(row) {
        let _this = this;
        let params = { ids: [row.id] };
        if (!Boolean(row.isShow)) {
          batchPutoff(params).then(res => {
            //
            // _this.getList();
          }).catch(res=>{
            _this.$message.error(this.$t("common.operationFailed"));
          });
        } else {
          batchPutOn(params).then(res => {
            // _this.getList();
          }).catch(res=>{
            _this.$message.error(this.$t("common.operationFailed"));
          });
        }
    },
    formatTime(t) {
      let date = new Date(t * 1000);
      let year = date.getFullYear();
      let month = String(date.getMonth() + 1).padStart(2, "0");
      let day = String(date.getDate()).padStart(2, "0");
      let hours = String(date.getHours()).padStart(2, "0");
      let minutes = String(date.getMinutes()).padStart(2, "0");
      let seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    editProduct(row) {
      this.productDialogVisible = true;
      this.dform = row;
      this.status = row.isShow ? "1" : "0";
      this.brandName = row.brand;

      this.dform.forMatUserCashBackRate=this.formatRate(this.dform.userCashBackRate)
      this.dform.forMatCashBackRate=this.formatRate(this.dform.cashBackRate)
      // this.dform.id = row.id
      // this.dform.storeName = row.storeName
      // this.dform.image = row.image
      // this.dform.price = row.price
      // this.dform.cashbackRate = row.cashbackRate
      // this.dform.cashbackAmount = row.cashbackAmount
      // this.dform.status = row.status === '1' ? '1' : '0'
      // this.productDialogVisible = true
    },
    isShowChange(row, val, type) {
      let _this = this;
      // let rows = [];

      let item = { id: row.id };
      if (val) {
        item[type] = true;
      } else {
        item[type] = false;
      }
      // rows.push(item);
      updateProductInfo(item)
        .then(res => {
          //_this.getList();
        })
        .catch(res => {
            this.$message.error(this.$t("common.operationFailed"));
        });
    },
    handleDelete(row) {
      let _this = this;
      this.$confirm(
        this.$t("brand.confirmOperation"),
        this.$t("brand.prompt"),
        {
          confirmButtonText: this.$t("brand.confirm"),
          cancelButtonText: this.$t("brand.cancel"),
          type: "warning",
          showClose: false
        }
      ).then(() => {
        let params = { ids: [row.id], type: "recycle" };
        batchDelete(params).then(res => {
          _this.getList();
        });
      });
    },
    handleCloseProductDialog() {
      this.productDialogVisible = false;
    },
    onSubProduct() {
      let _this = this;
      if (this.dform && this.dform.id) {
        updateProductInfo({
          id: this.dform.id,
          isShow: this.status == "1" ? true : false,
          brand: this.brandName
        })
          .then(res => {
            _this.getList();
            _this.productDialogVisible = false;
          })
          .catch(res => {
            _this.productDialogVisible = false;
            this.$message.error(this.$t("common.operationFailed"));
          });
      }
    },
    fetchProduct() {
      let _this = this;
      fetchProductApi(6, this.url)
        .then(res => {
          _this.dform = res;
          _this.dform.userCashBackRate = _this.formatRate(
            _this.dform.userCashBackRate
          );
          _this.dform.cashBackRate = _this.formatRate(_this.dform.cashBackRate);

          // this.dform.storeName = res.storeName
          // this.dform.image = res.image
          // this.dform.price = res.price
          // this.dform.cashbackRate = res.cashBackRate
          // this.dform.cashbackAmount = res.cashBackAmount
        })
        .catch(res => {
          this.$message.error(this.$t("product.fetchProductFailed"));
        });
    },
    formatRate(s) {
      return parseInt(s * 10000) / 100 + "%";
    }
  }
};
</script>

<style scoped lang="scss">
.padtop-10 {
  margin-top: 10px;
}
.width200 {
  width: 200px;
}
</style>
<style>
.readonly-input .el-input__inner {
  background-color: #f5f7fa;
  color: #606266;
  cursor: not-allowed;
}
</style>
