1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.genconusantara.milestone"
4    android:versionCode="30"
5    android:versionName="1.0.3" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:5:5-79
12-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:6:5-79
13-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:6:22-76
14    <uses-permission android:name="android.permission.SET_WALLPAPER" />
14-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:7:5-71
14-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:7:22-69
15    <uses-permission
15-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:8:5-9:38
16        android:name="android.permission.READ_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:8:22-77
17        android:maxSdkVersion="32" />
17-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:9:9-35
18    <uses-permission
18-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:10:5-11:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:10:22-78
20        android:maxSdkVersion="32" />
20-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:11:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
21-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:12:5-75
21-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:12:22-73
22    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
22-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:13:5-74
22-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:13:22-72
23    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
23-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:14:5-76
23-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:14:22-73
24    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
24-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:15:5-81
24-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:15:22-78
25    <!--
26         Required to query activities that can process text, see:
27         https://developer.android.com/training/package-visibility and
28         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
29
30         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
31    -->
32    <queries>
32-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:69:5-76:15
33        <package android:name="com.zhiliaoapp.musically" />
33-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:70:9-60
33-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:70:18-57
34        <package android:name="com.ss.android.ugc.trill" />
34-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:71:9-60
34-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:71:18-57
35
36        <intent>
36-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:72:9-75:18
37            <action android:name="android.intent.action.PROCESS_TEXT" />
37-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:73:13-72
37-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:73:21-70
38
39            <data android:mimeType="text/plain" />
39-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:50:17-54:53
39-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\debug\AndroidManifest.xml:73:19-48
40        </intent>
41        <intent>
41-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:78:9-81:18
42            <action android:name="android.intent.action.VIEW" />
42-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:45:17-69
42-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:45:25-66
43
44            <data android:scheme="https" />
44-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:50:17-54:53
44-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:51:21-43
45        </intent>
46        <intent>
46-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:82:9-85:18
47            <action android:name="android.intent.action.SEND" />
47-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:83:13-65
47-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:83:21-62
48
49            <data android:mimeType="text/plain" />
49-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:50:17-54:53
49-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\debug\AndroidManifest.xml:73:19-48
50        </intent>
51    </queries>
52
53    <permission
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
54        android:name="com.genconusantara.milestone.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.genconusantara.milestone.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
58
59    <application
59-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:17:5-63:19
60        android:name="com.genconusantara.milestone.MilestoneApplication"
60-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:19:9-73
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
62        android:debuggable="true"
63        android:extractNativeLibs="true"
64        android:icon="@mipmap/ic_launcher"
64-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:20:9-43
65        android:label="Genco" >
65-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:18:9-30
66        <activity
66-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:21:9-56:20
67            android:name="com.genconusantara.milestone.MainActivity"
67-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:22:13-41
68            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
68-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:27:13-163
69            android:exported="true"
69-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:23:13-36
70            android:hardwareAccelerated="true"
70-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:28:13-47
71            android:launchMode="singleTop"
71-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:24:13-43
72            android:taskAffinity=""
72-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:25:13-36
73            android:theme="@style/LaunchTheme"
73-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:26:13-47
74            android:windowSoftInputMode="adjustResize" >
74-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:29:13-55
75
76            <!--
77                 Specifies an Android theme to apply to this Activity as soon as
78                 the Android process has started. This theme is visible to the user
79                 while the Flutter UI initializes. After that, this theme continues
80                 to determine the Window background behind the Flutter UI.
81            -->
82            <meta-data
82-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:34:13-37:17
83                android:name="io.flutter.embedding.android.NormalTheme"
83-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:35:15-70
84                android:resource="@style/NormalTheme" />
84-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:36:15-52
85
86            <intent-filter>
86-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:38:13-41:29
87                <action android:name="android.intent.action.MAIN" />
87-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:39:17-68
87-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:39:25-66
88
89                <category android:name="android.intent.category.LAUNCHER" />
89-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:40:17-76
89-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:40:27-74
90            </intent-filter>
91            <intent-filter android:autoVerify="true" >
91-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:43:13-55:29
91-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:43:28-53
92                <action android:name="android.intent.action.VIEW" />
92-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:45:17-69
92-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:45:25-66
93
94                <category android:name="android.intent.category.DEFAULT" />
94-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:47:17-76
94-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:47:27-73
95                <category android:name="android.intent.category.BROWSABLE" />
95-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:48:17-78
95-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:48:27-75
96
97                <data
97-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:50:17-54:53
98                    android:host="genconusantara.com"
98-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:52:21-54
99                    android:pathPrefix="/callback"
99-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:53:21-51
100                    android:scheme="https" />
100-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:51:21-43
101            </intent-filter>
102        </activity>
103        <!--
104             Don't delete the meta-data below.
105             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
106        -->
107        <meta-data
107-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:59:9-61:33
108            android:name="flutterEmbedding"
108-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:60:13-44
109            android:value="2" />
109-->C:\Users\<USER>\Desktop\project\genco\appUI\android\app\src\main\AndroidManifest.xml:61:13-30
110        <!--
111           Declares a provider which allows us to store files to share in
112           '.../caches/share_plus' and grant the receiving action access
113        -->
114        <provider
114-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
115            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
115-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
116            android:authorities="com.genconusantara.milestone.flutter.share_provider"
116-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
117            android:exported="false"
117-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
118            android:grantUriPermissions="true" >
118-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
119            <meta-data
119-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
120                android:name="android.support.FILE_PROVIDER_PATHS"
120-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
121                android:resource="@xml/flutter_share_file_paths" />
121-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
122        </provider>
123        <!--
124           This manifest declared broadcast receiver allows us to use an explicit
125           Intent when creating a PendingItent to be informed of the user's choice
126        -->
127        <receiver
127-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
128            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
128-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
129            android:exported="false" >
129-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
130            <intent-filter>
130-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
131                <action android:name="EXTRA_CHOSEN_COMPONENT" />
131-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
131-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
132            </intent-filter>
133        </receiver>
134
135        <provider
135-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
136            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
136-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
137            android:authorities="com.genconusantara.milestone.flutter.image_provider"
137-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
138            android:exported="false"
138-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
139            android:grantUriPermissions="true" >
139-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
140            <meta-data
140-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
141                android:name="android.support.FILE_PROVIDER_PATHS"
141-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
142                android:resource="@xml/flutter_image_picker_file_paths" />
142-->[:share_plus] C:\Users\<USER>\Desktop\project\genco\appUI\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
143        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
144        <service
144-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
145            android:name="com.google.android.gms.metadata.ModuleDependencies"
145-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
146            android:enabled="false"
146-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
147            android:exported="false" >
147-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
148            <intent-filter>
148-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
149                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
149-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
149-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
150            </intent-filter>
151
152            <meta-data
152-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
153                android:name="photopicker_activity:0:required"
153-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
154                android:value="" />
154-->[:image_picker_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
155        </service>
156
157        <activity
157-->[:url_launcher_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
158            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
158-->[:url_launcher_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
159            android:exported="false"
159-->[:url_launcher_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
160            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
160-->[:url_launcher_android] C:\Users\<USER>\Desktop\project\genco\appUI\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
161
162        <provider
162-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
163            android:name="androidx.startup.InitializationProvider"
163-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
164            android:authorities="com.genconusantara.milestone.androidx-startup"
164-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
165            android:exported="false" >
165-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
166            <meta-data
166-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
167                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
167-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
168                android:value="androidx.startup" />
168-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
169            <meta-data
169-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
170                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
170-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
171                android:value="androidx.startup" />
171-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
172        </provider>
173
174        <uses-library
174-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
175            android:name="androidx.window.extensions"
175-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
176            android:required="false" />
176-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
177        <uses-library
177-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
178            android:name="androidx.window.sidecar"
178-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
179            android:required="false" />
179-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
180
181        <receiver
181-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
182            android:name="androidx.profileinstaller.ProfileInstallReceiver"
182-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
183            android:directBootAware="false"
183-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
184            android:enabled="true"
184-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
185            android:exported="true"
185-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
186            android:permission="android.permission.DUMP" >
186-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
187            <intent-filter>
187-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
188                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
188-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
188-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
189            </intent-filter>
190            <intent-filter>
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
191                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
192            </intent-filter>
193            <intent-filter>
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
194                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
195            </intent-filter>
196            <intent-filter>
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
197                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09d12e707b508dd64098ad851ea0c787\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
198            </intent-filter>
199        </receiver>
200    </application>
201
202</manifest>
