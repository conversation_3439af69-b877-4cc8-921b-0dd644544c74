// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Home`
  String get home_navigation_home {
    return Intl.message(
      'Home',
      name: 'home_navigation_home',
      desc: '',
      args: [],
    );
  }

  /// `Brand`
  String get home_navigation_brand {
    return Intl.message(
      'Brand',
      name: 'home_navigation_brand',
      desc: '',
      args: [],
    );
  }

  /// `Income`
  String get home_navigation_income {
    return Intl.message(
      'Income',
      name: 'home_navigation_income',
      desc: '',
      args: [],
    );
  }

  /// `Mine`
  String get home_navigation_mine {
    return Intl.message(
      'Mine',
      name: 'home_navigation_mine',
      desc: '',
      args: [],
    );
  }

  /// `Phone Login`
  String get login_mobile_title {
    return Intl.message(
      'Phone Login',
      name: 'login_mobile_title',
      desc: '',
      args: [],
    );
  }

  /// `Welcome back`
  String get login_welcome_back {
    return Intl.message(
      'Welcome back',
      name: 'login_welcome_back',
      desc: '',
      args: [],
    );
  }

  /// `Please login with your phone number`
  String get login_mobile_subtitle {
    return Intl.message(
      'Please login with your phone number',
      name: 'login_mobile_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Phone Number`
  String get login_phone_label {
    return Intl.message(
      'Phone Number',
      name: 'login_phone_label',
      desc: '',
      args: [],
    );
  }

  /// `Enter your phone number`
  String get login_phone_hint {
    return Intl.message(
      'Enter your phone number',
      name: 'login_phone_hint',
      desc: '',
      args: [],
    );
  }

  /// `Verification Code`
  String get login_code_label {
    return Intl.message(
      'Verification Code',
      name: 'login_code_label',
      desc: '',
      args: [],
    );
  }

  /// `Enter the verification code`
  String get login_code_hint {
    return Intl.message(
      'Enter the verification code',
      name: 'login_code_hint',
      desc: '',
      args: [],
    );
  }

  /// `Get Code`
  String get login_get_code {
    return Intl.message('Get Code', name: 'login_get_code', desc: '', args: []);
  }

  /// `s`
  String get login_code_seconds {
    return Intl.message('s', name: 'login_code_seconds', desc: '', args: []);
  }

  /// `Login`
  String get login_button {
    return Intl.message('Login', name: 'login_button', desc: '', args: []);
  }

  /// `Login with Password`
  String get login_password_alternative {
    return Intl.message(
      'Login with Password',
      name: 'login_password_alternative',
      desc: '',
      args: [],
    );
  }

  /// `Please enter phone number`
  String get login_enter_phone {
    return Intl.message(
      'Please enter phone number',
      name: 'login_enter_phone',
      desc: '',
      args: [],
    );
  }

  /// `Verification code sent`
  String get login_code_sent {
    return Intl.message(
      'Verification code sent',
      name: 'login_code_sent',
      desc: '',
      args: [],
    );
  }

  /// `Failed to send code`
  String get login_send_failed {
    return Intl.message(
      'Failed to send code',
      name: 'login_send_failed',
      desc: '',
      args: [],
    );
  }

  /// `Please enter phone number and code`
  String get login_enter_phone_code {
    return Intl.message(
      'Please enter phone number and code',
      name: 'login_enter_phone_code',
      desc: '',
      args: [],
    );
  }

  /// `Login failed`
  String get login_failed {
    return Intl.message(
      'Login failed',
      name: 'login_failed',
      desc: '',
      args: [],
    );
  }

  /// `Oops! Something went wrong. Please try again later.`
  String get error_title {
    return Intl.message(
      'Oops! Something went wrong. Please try again later.',
      name: 'error_title',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get error_button_title {
    return Intl.message(
      'Retry',
      name: 'error_button_title',
      desc: '',
      args: [],
    );
  }

  /// `Loading failed, try again`
  String get loading_more_error {
    return Intl.message(
      'Loading failed, try again',
      name: 'loading_more_error',
      desc: '',
      args: [],
    );
  }

  /// `Oh it's over!`
  String get loading_more_no_more {
    return Intl.message(
      'Oh it\'s over!',
      name: 'loading_more_no_more',
      desc: '',
      args: [],
    );
  }

  /// `No wallpaper yet ~`
  String get loading_more_empty {
    return Intl.message(
      'No wallpaper yet ~',
      name: 'loading_more_empty',
      desc: '',
      args: [],
    );
  }

  /// `write a rating`
  String get loading_more_write {
    return Intl.message(
      'write a rating',
      name: 'loading_more_write',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get loading_more_retry {
    return Intl.message(
      'Retry',
      name: 'loading_more_retry',
      desc: '',
      args: [],
    );
  }

  /// `Cashback Rate`
  String get home_rebate_rate_title {
    return Intl.message(
      'Cashback Rate',
      name: 'home_rebate_rate_title',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get home_platform_all {
    return Intl.message('All', name: 'home_platform_all', desc: '', args: []);
  }

  /// `Hottest`
  String get home_platform_hot_sale {
    return Intl.message(
      'Hottest',
      name: 'home_platform_hot_sale',
      desc: '',
      args: [],
    );
  }

  /// `High Rebate`
  String get home_platform_high_rebate {
    return Intl.message(
      'High Rebate',
      name: 'home_platform_high_rebate',
      desc: '',
      args: [],
    );
  }

  /// `TikTok`
  String get home_platform_tiktok {
    return Intl.message(
      'TikTok',
      name: 'home_platform_tiktok',
      desc: '',
      args: [],
    );
  }

  /// `Shopee`
  String get home_platform_shopee {
    return Intl.message(
      'Shopee',
      name: 'home_platform_shopee',
      desc: '',
      args: [],
    );
  }

  /// `Check & Earn Rebates`
  String get home_logo_slogan {
    return Intl.message(
      'Check & Earn Rebates',
      name: 'home_logo_slogan',
      desc: '',
      args: [],
    );
  }

  /// `Copy product link, get cashback`
  String get home_search_placeholder {
    return Intl.message(
      'Copy product link, get cashback',
      name: 'home_search_placeholder',
      desc: '',
      args: [],
    );
  }

  /// `Paste`
  String get home_search_button_title {
    return Intl.message(
      'Paste',
      name: 'home_search_button_title',
      desc: '',
      args: [],
    );
  }

  /// `Copy`
  String get home_search_instructions_copy {
    return Intl.message(
      'Copy',
      name: 'home_search_instructions_copy',
      desc: '',
      args: [],
    );
  }

  /// `Open link in GENCO to get cashback`
  String get home_search_instructions_text {
    return Intl.message(
      'Open link in GENCO to get cashback',
      name: 'home_search_instructions_text',
      desc: '',
      args: [],
    );
  }

  /// `Check All`
  String get home_cashback_instructions_check_all {
    return Intl.message(
      'Check All',
      name: 'home_cashback_instructions_check_all',
      desc: '',
      args: [],
    );
  }

  /// `Earn Cashback Process`
  String get home_cashback_instructions_title {
    return Intl.message(
      'Earn Cashback Process',
      name: 'home_cashback_instructions_title',
      desc: '',
      args: [],
    );
  }

  /// `Copy product\nlink`
  String get home_cashback_instructions_step1 {
    return Intl.message(
      'Copy product\nlink',
      name: 'home_cashback_instructions_step1',
      desc: '',
      args: [],
    );
  }

  /// `Open GENCO\ncheck cashback`
  String get home_cashback_instructions_step2 {
    return Intl.message(
      'Open GENCO\ncheck cashback',
      name: 'home_cashback_instructions_step2',
      desc: '',
      args: [],
    );
  }

  /// `Go Shopee/\nTikTok`
  String get home_cashback_instructions_step3 {
    return Intl.message(
      'Go Shopee/\nTikTok',
      name: 'home_cashback_instructions_step3',
      desc: '',
      args: [],
    );
  }

  /// `Get cashback`
  String get home_cashback_instructions_step4 {
    return Intl.message(
      'Get cashback',
      name: 'home_cashback_instructions_step4',
      desc: '',
      args: [],
    );
  }

  /// `Est. Cashback`
  String get home_cashback_button_title {
    return Intl.message(
      'Est. Cashback',
      name: 'home_cashback_button_title',
      desc: '',
      args: [],
    );
  }

  /// `Price`
  String get detail_price_title {
    return Intl.message(
      'Price',
      name: 'detail_price_title',
      desc: '',
      args: [],
    );
  }

  /// `Sold out`
  String get detail_sold {
    return Intl.message('Sold out', name: 'detail_sold', desc: '', args: []);
  }

  /// ``
  String get detail_sold_count {
    return Intl.message('', name: 'detail_sold_count', desc: '', args: []);
  }

  /// `Estimated Rebate Amount`
  String get detail_cashback_amount {
    return Intl.message(
      'Estimated Rebate Amount',
      name: 'detail_cashback_amount',
      desc: '',
      args: [],
    );
  }

  /// `Rebate Rate`
  String get detail_rebate_rate {
    return Intl.message(
      'Rebate Rate',
      name: 'detail_rebate_rate',
      desc: '',
      args: [],
    );
  }

  /// `Cashback Earning Process`
  String get detail_cashback_flow_title {
    return Intl.message(
      'Cashback Earning Process',
      name: 'detail_cashback_flow_title',
      desc: '',
      args: [],
    );
  }

  /// `View Tutorial`
  String get detail_cashback_flow_check {
    return Intl.message(
      'View Tutorial',
      name: 'detail_cashback_flow_check',
      desc: '',
      args: [],
    );
  }

  /// `Click Product`
  String get detail_cashback_flow_step1 {
    return Intl.message(
      'Click Product',
      name: 'detail_cashback_flow_step1',
      desc: '',
      args: [],
    );
  }

  /// `Order on\nTikTok`
  String get detail_cashback_flow_step2 {
    return Intl.message(
      'Order on\nTikTok',
      name: 'detail_cashback_flow_step2',
      desc: '',
      args: [],
    );
  }

  /// `Track Order`
  String get detail_cashback_flow_step3 {
    return Intl.message(
      'Track Order',
      name: 'detail_cashback_flow_step3',
      desc: '',
      args: [],
    );
  }

  /// `Get Cashback`
  String get detail_cashback_flow_step4 {
    return Intl.message(
      'Get Cashback',
      name: 'detail_cashback_flow_step4',
      desc: '',
      args: [],
    );
  }

  /// `Total`
  String get detail_brand_product_amount_pre {
    return Intl.message(
      'Total',
      name: 'detail_brand_product_amount_pre',
      desc: '',
      args: [],
    );
  }

  /// `products`
  String get detail_brand_product_amount {
    return Intl.message(
      'products',
      name: 'detail_brand_product_amount',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get brand_filter_all {
    return Intl.message('All', name: 'brand_filter_all', desc: '', args: []);
  }

  /// `Price`
  String get brand_filter_price {
    return Intl.message(
      'Price',
      name: 'brand_filter_price',
      desc: '',
      args: [],
    );
  }

  /// `Cashback Rate`
  String get brand_filter_rebate_rate {
    return Intl.message(
      'Cashback Rate',
      name: 'brand_filter_rebate_rate',
      desc: '',
      args: [],
    );
  }

  /// `Sales`
  String get brand_filter_sales {
    return Intl.message(
      'Sales',
      name: 'brand_filter_sales',
      desc: '',
      args: [],
    );
  }

  /// `New`
  String get brand_filter_latest {
    return Intl.message('New', name: 'brand_filter_latest', desc: '', args: []);
  }

  /// `User Guide`
  String get usage_guideline_title {
    return Intl.message(
      'User Guide',
      name: 'usage_guideline_title',
      desc: '',
      args: [],
    );
  }

  /// `Get cashback on Shopee & TikTok in 3 steps via link`
  String get usage_guideline_description {
    return Intl.message(
      'Get cashback on Shopee & TikTok in 3 steps via link',
      name: 'usage_guideline_description',
      desc: '',
      args: [],
    );
  }

  /// `Open product on Shopee/TikTok and copy link`
  String get usage_guideline_step1 {
    return Intl.message(
      'Open product on Shopee/TikTok and copy link',
      name: 'usage_guideline_step1',
      desc: '',
      args: [],
    );
  }

  /// `Paste link in GENCO to check cashback`
  String get usage_guideline_step2 {
    return Intl.message(
      'Paste link in GENCO to check cashback',
      name: 'usage_guideline_step2',
      desc: '',
      args: [],
    );
  }

  /// `Jump to Shopee/TikTok to complete order`
  String get usage_guideline_step3 {
    return Intl.message(
      'Jump to Shopee/TikTok to complete order',
      name: 'usage_guideline_step3',
      desc: '',
      args: [],
    );
  }

  /// `<red>Carefully Selected</red> Brands`
  String get brand_home_top_title {
    return Intl.message(
      '<red>Carefully Selected</red> Brands',
      name: 'brand_home_top_title',
      desc: '',
      args: [],
    );
  }

  /// `Discover favorites while enjoying great shopping`
  String get brand_home_top_subtitle {
    return Intl.message(
      'Discover favorites while enjoying great shopping',
      name: 'brand_home_top_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `TikTok Hot Brands`
  String get brand_tiktok_hot_sale_title {
    return Intl.message(
      'TikTok Hot Brands',
      name: 'brand_tiktok_hot_sale_title',
      desc: '',
      args: [],
    );
  }

  /// `High Cashback Brands`
  String get brand_high_rebate_title {
    return Intl.message(
      'High Cashback Brands',
      name: 'brand_high_rebate_title',
      desc: '',
      args: [],
    );
  }

  /// `Highest Cashback Rate`
  String get brand_highest_rebate_rate {
    return Intl.message(
      'Highest Cashback Rate',
      name: 'brand_highest_rebate_rate',
      desc: '',
      args: [],
    );
  }

  /// `No Data`
  String get message_no_data {
    return Intl.message('No Data', name: 'message_no_data', desc: '', args: []);
  }

  /// `Estimated Total Income`
  String get income_pre_total_income {
    return Intl.message(
      'Estimated Total Income',
      name: 'income_pre_total_income',
      desc: '',
      args: [],
    );
  }

  /// `Today's Income`
  String get income_today {
    return Intl.message(
      'Today\'s Income',
      name: 'income_today',
      desc: '',
      args: [],
    );
  }

  /// `Pending Credit`
  String get income_amount_to_be_credited {
    return Intl.message(
      'Pending Credit',
      name: 'income_amount_to_be_credited',
      desc: '',
      args: [],
    );
  }

  /// `Includes unconfirmed order cashback. For reference only.`
  String get income_amount_to_be_credited_hint {
    return Intl.message(
      'Includes unconfirmed order cashback. For reference only.',
      name: 'income_amount_to_be_credited_hint',
      desc: '',
      args: [],
    );
  }

  /// `Credited Amount`
  String get income_amount_credited {
    return Intl.message(
      'Credited Amount',
      name: 'income_amount_credited',
      desc: '',
      args: [],
    );
  }

  /// `Successfully credited amount`
  String get income_amount_credited_description {
    return Intl.message(
      'Successfully credited amount',
      name: 'income_amount_credited_description',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawable Amount`
  String get income_amount_available_for_withdrawal {
    return Intl.message(
      'Withdrawable Amount',
      name: 'income_amount_available_for_withdrawal',
      desc: '',
      args: [],
    );
  }

  /// `Withdraw`
  String get income_withdrawal_button {
    return Intl.message(
      'Withdraw',
      name: 'income_withdrawal_button',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Success`
  String get income_withdrawal_success {
    return Intl.message(
      'Withdrawal Success',
      name: 'income_withdrawal_success',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Failed`
  String get income_withdrawal_failed {
    return Intl.message(
      'Withdrawal Failed',
      name: 'income_withdrawal_failed',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Amount`
  String get income_withdrawal_amount {
    return Intl.message(
      'Withdrawal Amount',
      name: 'income_withdrawal_amount',
      desc: '',
      args: [],
    );
  }

  /// `Enter amount`
  String get income_withdrawal_amount_hint {
    return Intl.message(
      'Enter amount',
      name: 'income_withdrawal_amount_hint',
      desc: '',
      args: [],
    );
  }

  /// `Transaction History`
  String get income_transaction_history {
    return Intl.message(
      'Transaction History',
      name: 'income_transaction_history',
      desc: '',
      args: [],
    );
  }

  /// `No transactions yet`
  String get income_transaction_history_empty {
    return Intl.message(
      'No transactions yet',
      name: 'income_transaction_history_empty',
      desc: '',
      args: [],
    );
  }

  /// `Transaction Details`
  String get income_transaction_detail {
    return Intl.message(
      'Transaction Details',
      name: 'income_transaction_detail',
      desc: '',
      args: [],
    );
  }

  /// `My Orders`
  String get income_my_order {
    return Intl.message(
      'My Orders',
      name: 'income_my_order',
      desc: '',
      args: [],
    );
  }

  /// `Income Details`
  String get income_income_detail {
    return Intl.message(
      'Income Details',
      name: 'income_income_detail',
      desc: '',
      args: [],
    );
  }

  /// `Estimated total income for reference. Final amount subject to actual payment.`
  String get income_pre_total_income_description {
    return Intl.message(
      'Estimated total income for reference. Final amount subject to actual payment.',
      name: 'income_pre_total_income_description',
      desc: '',
      args: [],
    );
  }

  /// `OK`
  String get ok {
    return Intl.message('OK', name: 'ok', desc: '', args: []);
  }

  /// `Complete`
  String get finish {
    return Intl.message('Complete', name: 'finish', desc: '', args: []);
  }

  /// `Cashback Income`
  String get credited_rebase_income {
    return Intl.message(
      'Cashback Income',
      name: 'credited_rebase_income',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get all {
    return Intl.message('All', name: 'all', desc: '', args: []);
  }

  /// `Income`
  String get income {
    return Intl.message('Income', name: 'income', desc: '', args: []);
  }

  /// `Expense`
  String get expenditure {
    return Intl.message('Expense', name: 'expenditure', desc: '', args: []);
  }

  /// `Cashback Income`
  String get rebase_income {
    return Intl.message(
      'Cashback Income',
      name: 'rebase_income',
      desc: '',
      args: [],
    );
  }

  /// `Cashback Expense`
  String get rebase_expenditure {
    return Intl.message(
      'Cashback Expense',
      name: 'rebase_expenditure',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Account`
  String get withdrawal_account {
    return Intl.message(
      'Withdrawal Account',
      name: 'withdrawal_account',
      desc: '',
      args: [],
    );
  }

  /// `Select account`
  String get please_select_withdrawal_account {
    return Intl.message(
      'Select account',
      name: 'please_select_withdrawal_account',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Amount`
  String get withdrawal_amount {
    return Intl.message(
      'Withdrawal Amount',
      name: 'withdrawal_amount',
      desc: '',
      args: [],
    );
  }

  /// `Max withdrawable`
  String get withdrawal_amount_hint {
    return Intl.message(
      'Max withdrawable',
      name: 'withdrawal_amount_hint',
      desc: '',
      args: [],
    );
  }

  /// `Min withdrawable`
  String get withdrawal_amount_min {
    return Intl.message(
      'Min withdrawable',
      name: 'withdrawal_amount_min',
      desc: '',
      args: [],
    );
  }

  /// `Withdraw All`
  String get withdrawal_all {
    return Intl.message(
      'Withdraw All',
      name: 'withdrawal_all',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Complete`
  String get withdrawal_finish {
    return Intl.message(
      'Withdrawal Complete',
      name: 'withdrawal_finish',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal submitted`
  String get withdrawal_success {
    return Intl.message(
      'Withdrawal submitted',
      name: 'withdrawal_success',
      desc: '',
      args: [],
    );
  }

  /// `Funds arrive within 24h (excl. weekends/holidays)`
  String get withdrawal_success_hint {
    return Intl.message(
      'Funds arrive within 24h (excl. weekends/holidays)',
      name: 'withdrawal_success_hint',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Failed`
  String get withdrawal_failed {
    return Intl.message(
      'Withdrawal Failed',
      name: 'withdrawal_failed',
      desc: '',
      args: [],
    );
  }

  /// `Fee`
  String get withdrawal_fees {
    return Intl.message('Fee', name: 'withdrawal_fees', desc: '', args: []);
  }

  /// `1.5% fee. Min fee Rp5.550. If <Rp5.550, fee charged at Rp5.550.`
  String get withdrawal_fees_hint {
    return Intl.message(
      '1.5% fee. Min fee Rp5.550. If <Rp5.550, fee charged at Rp5.550.',
      name: 'withdrawal_fees_hint',
      desc: '',
      args: [],
    );
  }

  /// `Note`
  String get withdrawal_hint {
    return Intl.message('Note', name: 'withdrawal_hint', desc: '', args: []);
  }

  /// `Funds arrive within 24h (excl. weekends/holidays)`
  String get withdrawal_hint_description {
    return Intl.message(
      'Funds arrive within 24h (excl. weekends/holidays)',
      name: 'withdrawal_hint_description',
      desc: '',
      args: [],
    );
  }

  /// `Transaction Type`
  String get trade_type {
    return Intl.message(
      'Transaction Type',
      name: 'trade_type',
      desc: '',
      args: [],
    );
  }

  /// `Time`
  String get trade_time {
    return Intl.message('Time', name: 'trade_time', desc: '', args: []);
  }

  /// `Transaction ID`
  String get trade_serial_number {
    return Intl.message(
      'Transaction ID',
      name: 'trade_serial_number',
      desc: '',
      args: [],
    );
  }

  /// `Channel`
  String get trade_channel {
    return Intl.message('Channel', name: 'trade_channel', desc: '', args: []);
  }

  /// `Order ID`
  String get trade_order_number {
    return Intl.message(
      'Order ID',
      name: 'trade_order_number',
      desc: '',
      args: [],
    );
  }

  /// `Order Cashback`
  String get income_order_rebate {
    return Intl.message(
      'Order Cashback',
      name: 'income_order_rebate',
      desc: '',
      args: [],
    );
  }

  /// `Campaign Reward`
  String get income_campaign_reward {
    return Intl.message(
      'Campaign Reward',
      name: 'income_campaign_reward',
      desc: '',
      args: [],
    );
  }

  /// `Estimated Cashback`
  String get income_expected_total_amount {
    return Intl.message(
      'Estimated Cashback',
      name: 'income_expected_total_amount',
      desc: '',
      args: [],
    );
  }

  /// `Estimated, subject to final credit`
  String get income_expected_total_amount_hint {
    return Intl.message(
      'Estimated, subject to final credit',
      name: 'income_expected_total_amount_hint',
      desc: '',
      args: [],
    );
  }

  /// `Actual Cashback`
  String get income_actual_credited_amount {
    return Intl.message(
      'Actual Cashback',
      name: 'income_actual_credited_amount',
      desc: '',
      args: [],
    );
  }

  /// `Credited to account`
  String get income_actual_credited_amount_hint {
    return Intl.message(
      'Credited to account',
      name: 'income_actual_credited_amount_hint',
      desc: '',
      args: [],
    );
  }

  /// `My Orders`
  String get order_title {
    return Intl.message('My Orders', name: 'order_title', desc: '', args: []);
  }

  /// `All`
  String get order_tab_all {
    return Intl.message('All', name: 'order_tab_all', desc: '', args: []);
  }

  /// `Processing`
  String get order_tab_processing {
    return Intl.message(
      'Processing',
      name: 'order_tab_processing',
      desc: '',
      args: [],
    );
  }

  /// `Completed`
  String get order_tab_completed {
    return Intl.message(
      'Completed',
      name: 'order_tab_completed',
      desc: '',
      args: [],
    );
  }

  /// `Expired`
  String get order_tab_expired {
    return Intl.message(
      'Expired',
      name: 'order_tab_expired',
      desc: '',
      args: [],
    );
  }

  /// `Application Time:`
  String get order_application_time {
    return Intl.message(
      'Application Time:',
      name: 'order_application_time',
      desc: '',
      args: [],
    );
  }

  /// `Processing`
  String get order_status_processing {
    return Intl.message(
      'Processing',
      name: 'order_status_processing',
      desc: '',
      args: [],
    );
  }

  /// `Completed`
  String get order_status_completed {
    return Intl.message(
      'Completed',
      name: 'order_status_completed',
      desc: '',
      args: [],
    );
  }

  /// `Expired`
  String get order_status_expired {
    return Intl.message(
      'Expired',
      name: 'order_status_expired',
      desc: '',
      args: [],
    );
  }

  /// `Est. Cashback:`
  String get order_expected_cashback {
    return Intl.message(
      'Est. Cashback:',
      name: 'order_expected_cashback',
      desc: '',
      args: [],
    );
  }

  /// `Cashback Info`
  String get order_cashback_info {
    return Intl.message(
      'Cashback Info',
      name: 'order_cashback_info',
      desc: '',
      args: [],
    );
  }

  /// `Cashback Guide`
  String get rebate_instruction_title {
    return Intl.message(
      'Cashback Guide',
      name: 'rebate_instruction_title',
      desc: '',
      args: [],
    );
  }

  /// `Cashback credited after order confirmation & completion.`
  String get rebate_instruction_content {
    return Intl.message(
      'Cashback credited after order confirmation & completion.',
      name: 'rebate_instruction_content',
      desc: '',
      args: [],
    );
  }

  /// `01`
  String get rebate_step_1_title {
    return Intl.message('01', name: 'rebate_step_1_title', desc: '', args: []);
  }

  /// `Order via Shopee/TikTok`
  String get rebate_step_1_content {
    return Intl.message(
      'Order via Shopee/TikTok',
      name: 'rebate_step_1_content',
      desc: '',
      args: [],
    );
  }

  /// `02`
  String get rebate_step_2_title {
    return Intl.message('02', name: 'rebate_step_2_title', desc: '', args: []);
  }

  /// `Track status in 1-3 days`
  String get rebate_step_2_content {
    return Intl.message(
      'Track status in 1-3 days',
      name: 'rebate_step_2_content',
      desc: '',
      args: [],
    );
  }

  /// `03`
  String get rebate_step_3_title {
    return Intl.message('03', name: 'rebate_step_3_title', desc: '', args: []);
  }

  /// `Get cashback after completion`
  String get rebate_step_3_content {
    return Intl.message(
      'Get cashback after completion',
      name: 'rebate_step_3_content',
      desc: '',
      args: [],
    );
  }

  /// `How to Earn Cashback:`
  String get rebate_how_to_get_title {
    return Intl.message(
      'How to Earn Cashback:',
      name: 'rebate_how_to_get_title',
      desc: '',
      args: [],
    );
  }

  /// `How to Order`
  String get rebate_how_to_order_title {
    return Intl.message(
      'How to Order',
      name: 'rebate_how_to_order_title',
      desc: '',
      args: [],
    );
  }

  /// `Place order via GENCO redirect to track cashback.`
  String get rebate_how_to_order_content {
    return Intl.message(
      'Place order via GENCO redirect to track cashback.',
      name: 'rebate_how_to_order_content',
      desc: '',
      args: [],
    );
  }

  /// `Keep GENCO as Last App`
  String get rebate_genco_last_app_title {
    return Intl.message(
      'Keep GENCO as Last App',
      name: 'rebate_genco_last_app_title',
      desc: '',
      args: [],
    );
  }

  /// `Cashback untraceable if other apps opened after GENCO.`
  String get rebate_genco_last_app_content {
    return Intl.message(
      'Cashback untraceable if other apps opened after GENCO.',
      name: 'rebate_genco_last_app_content',
      desc: '',
      args: [],
    );
  }

  /// `Ineligible Orders`
  String get rebate_unsupported_order_title {
    return Intl.message(
      'Ineligible Orders',
      name: 'rebate_unsupported_order_title',
      desc: '',
      args: [],
    );
  }

  /// `Orders not placed via GENCO redirect are ineligible.`
  String get rebate_unsupported_order_content {
    return Intl.message(
      'Orders not placed via GENCO redirect are ineligible.',
      name: 'rebate_unsupported_order_content',
      desc: '',
      args: [],
    );
  }

  /// `Cashback Details`
  String get rebase_info {
    return Intl.message(
      'Cashback Details',
      name: 'rebase_info',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Price`
  String get rebase_price {
    return Intl.message(
      'Purchase Price',
      name: 'rebase_price',
      desc: '',
      args: [],
    );
  }

  /// `Cashback Rate`
  String get rebase_rate {
    return Intl.message(
      'Cashback Rate',
      name: 'rebase_rate',
      desc: '',
      args: [],
    );
  }

  /// `Cashback Earned`
  String get rebase_cash {
    return Intl.message(
      'Cashback Earned',
      name: 'rebase_cash',
      desc: '',
      args: [],
    );
  }

  /// `Select Account`
  String get withdrawal_choose_method {
    return Intl.message(
      'Select Account',
      name: 'withdrawal_choose_method',
      desc: '',
      args: [],
    );
  }

  /// `Add Bank Card`
  String get withdrawal_add_card {
    return Intl.message(
      'Add Bank Card',
      name: 'withdrawal_add_card',
      desc: '',
      args: [],
    );
  }

  /// `Add e-Wallet`
  String get withdrawal_add_e_card {
    return Intl.message(
      'Add e-Wallet',
      name: 'withdrawal_add_e_card',
      desc: '',
      args: [],
    );
  }

  /// `Name`
  String get name {
    return Intl.message('Name', name: 'name', desc: '', args: []);
  }

  /// `Enter name`
  String get name_placeholder {
    return Intl.message(
      'Enter name',
      name: 'name_placeholder',
      desc: '',
      args: [],
    );
  }

  /// `Bank Name`
  String get bank_name {
    return Intl.message('Bank Name', name: 'bank_name', desc: '', args: []);
  }

  /// `Select Bank`
  String get select_bank {
    return Intl.message('Select Bank', name: 'select_bank', desc: '', args: []);
  }

  /// `Card Number`
  String get bank_card_number {
    return Intl.message(
      'Card Number',
      name: 'bank_card_number',
      desc: '',
      args: [],
    );
  }

  /// `Enter card number`
  String get input_bank_card_number {
    return Intl.message(
      'Enter card number',
      name: 'input_bank_card_number',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get search {
    return Intl.message('Search', name: 'search', desc: '', args: []);
  }

  /// `Continue`
  String get button_next {
    return Intl.message('Continue', name: 'button_next', desc: '', args: []);
  }

  /// `Confirm Card`
  String get bind_bank_card_confirm {
    return Intl.message(
      'Confirm Card',
      name: 'bind_bank_card_confirm',
      desc: '',
      args: [],
    );
  }

  /// `E-Wallet`
  String get e_wallet {
    return Intl.message('E-Wallet', name: 'e_wallet', desc: '', args: []);
  }

  /// `Phone`
  String get phone_number {
    return Intl.message('Phone', name: 'phone_number', desc: '', args: []);
  }

  /// `Enter Phone Number`
  String get phone_number_placeholder {
    return Intl.message(
      'Enter Phone Number',
      name: 'phone_number_placeholder',
      desc: '',
      args: [],
    );
  }

  /// `Select e-Wallet`
  String get select_e_wallet {
    return Intl.message(
      'Select e-Wallet',
      name: 'select_e_wallet',
      desc: '',
      args: [],
    );
  }

  /// `Save with GENCO`
  String get usage_hint {
    return Intl.message(
      'Save with GENCO',
      name: 'usage_hint',
      desc: '',
      args: [],
    );
  }

  /// `My Collections`
  String get my_collection {
    return Intl.message(
      'My Collections',
      name: 'my_collection',
      desc: '',
      args: [],
    );
  }

  /// `4 Steps to Cashback`
  String get guide_step1_title {
    return Intl.message(
      '4 Steps to Cashback',
      name: 'guide_step1_title',
      desc: '',
      args: [],
    );
  }

  /// `Copy product link on Shopee/TikTok`
  String get guide_step1_content_flow_1 {
    return Intl.message(
      'Copy product link on Shopee/TikTok',
      name: 'guide_step1_content_flow_1',
      desc: '',
      args: [],
    );
  }

  /// `Check cashback in GENCO`
  String get guide_step1_content_flow_2 {
    return Intl.message(
      'Check cashback in GENCO',
      name: 'guide_step1_content_flow_2',
      desc: '',
      args: [],
    );
  }

  /// `Order on Shopee/TikTok`
  String get guide_step1_content_flow_3 {
    return Intl.message(
      'Order on Shopee/TikTok',
      name: 'guide_step1_content_flow_3',
      desc: '',
      args: [],
    );
  }

  /// `Confirm order & get cashback`
  String get guide_step1_content_flow_4 {
    return Intl.message(
      'Confirm order & get cashback',
      name: 'guide_step1_content_flow_4',
      desc: '',
      args: [],
    );
  }

  /// `Copy Product Link`
  String get guide_step1_content_flow_1_title {
    return Intl.message(
      'Copy Product Link',
      name: 'guide_step1_content_flow_1_title',
      desc: '',
      args: [],
    );
  }

  /// `Tap 'Share' > 'Copy Link' on Shopee/TikTok`
  String get guide_step1_content_flow_1_description {
    return Intl.message(
      'Tap \'Share\' > \'Copy Link\' on Shopee/TikTok',
      name: 'guide_step1_content_flow_1_description',
      desc: '',
      args: [],
    );
  }

  /// `Check Cashback in GENCO`
  String get guide_step1_content_flow_2_title {
    return Intl.message(
      'Check Cashback in GENCO',
      name: 'guide_step1_content_flow_2_title',
      desc: '',
      args: [],
    );
  }

  /// `Paste link in GENCO to see cashback`
  String get guide_step1_content_flow_2_description {
    return Intl.message(
      'Paste link in GENCO to see cashback',
      name: 'guide_step1_content_flow_2_description',
      desc: '',
      args: [],
    );
  }

  /// `Tap 'Order on Shopee/TikTok'`
  String get guide_step1_content_flow_3_title {
    return Intl.message(
      'Tap \'Order on Shopee/TikTok\'',
      name: 'guide_step1_content_flow_3_title',
      desc: '',
      args: [],
    );
  }

  /// `Jump to Shopee/TikTok to buy`
  String get guide_step1_content_flow_3_description {
    return Intl.message(
      'Jump to Shopee/TikTok to buy',
      name: 'guide_step1_content_flow_3_description',
      desc: '',
      args: [],
    );
  }

  /// `Track & Get Cashback`
  String get guide_step1_content_flow_4_title {
    return Intl.message(
      'Track & Get Cashback',
      name: 'guide_step1_content_flow_4_title',
      desc: '',
      args: [],
    );
  }

  /// `Cashback credited after order confirmation`
  String get guide_step1_content_flow_4_description {
    return Intl.message(
      'Cashback credited after order confirmation',
      name: 'guide_step1_content_flow_4_description',
      desc: '',
      args: [],
    );
  }

  /// `How to Get Cashback?`
  String get guide_step1_content_flow_5_title {
    return Intl.message(
      'How to Get Cashback?',
      name: 'guide_step1_content_flow_5_title',
      desc: '',
      args: [],
    );
  }

  /// `Watch tutorial video for details`
  String get guide_step1_content_flow_5_description {
    return Intl.message(
      'Watch tutorial video for details',
      name: 'guide_step1_content_flow_5_description',
      desc: '',
      args: [],
    );
  }

  /// `Cashback`
  String get feedback_cash {
    return Intl.message('Cashback', name: 'feedback_cash', desc: '', args: []);
  }

  /// `Edit`
  String get edit {
    return Intl.message('Edit', name: 'edit', desc: '', args: []);
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `Select All`
  String get select_all {
    return Intl.message('Select All', name: 'select_all', desc: '', args: []);
  }

  /// `Deselect All`
  String get cancel_select_all {
    return Intl.message(
      'Deselect All',
      name: 'cancel_select_all',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get delete {
    return Intl.message('Delete', name: 'delete', desc: '', args: []);
  }

  /// `Settings`
  String get setting {
    return Intl.message('Settings', name: 'setting', desc: '', args: []);
  }

  /// `My Avatar`
  String get my_avatar {
    return Intl.message('My Avatar', name: 'my_avatar', desc: '', args: []);
  }

  /// `Nickname`
  String get nickname {
    return Intl.message('Nickname', name: 'nickname', desc: '', args: []);
  }

  /// `WhatsApp`
  String get whatsapp_account {
    return Intl.message(
      'WhatsApp',
      name: 'whatsapp_account',
      desc: '',
      args: [],
    );
  }

  /// `Change Phone`
  String get modify_phone_number {
    return Intl.message(
      'Change Phone',
      name: 'modify_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Change Password`
  String get modify_password {
    return Intl.message(
      'Change Password',
      name: 'modify_password',
      desc: '',
      args: [],
    );
  }

  /// `Privacy`
  String get privacy {
    return Intl.message('Privacy', name: 'privacy', desc: '', args: []);
  }

  /// `About`
  String get about {
    return Intl.message('About', name: 'about', desc: '', args: []);
  }

  /// `Change Nickname`
  String get modify_nickname {
    return Intl.message(
      'Change Nickname',
      name: 'modify_nickname',
      desc: '',
      args: [],
    );
  }

  /// `Login with TikTok`
  String get login_with_tiktok {
    return Intl.message(
      'Login with TikTok',
      name: 'login_with_tiktok',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to GENCO`
  String get login_title {
    return Intl.message(
      'Welcome to GENCO',
      name: 'login_title',
      desc: '',
      args: [],
    );
  }

  /// `Save on shopping,\nEarn by sharing`
  String get login_subtitle {
    return Intl.message(
      'Save on shopping,\nEarn by sharing',
      name: 'login_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Enter WhatsApp number`
  String get whatsapp_account_hint {
    return Intl.message(
      'Enter WhatsApp number',
      name: 'whatsapp_account_hint',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get next_step {
    return Intl.message('Next', name: 'next_step', desc: '', args: []);
  }

  /// `Account required`
  String get account_empty_hint {
    return Intl.message(
      'Account required',
      name: 'account_empty_hint',
      desc: '',
      args: [],
    );
  }

  /// `Enter OTP`
  String get input_opt_verification_code {
    return Intl.message(
      'Enter OTP',
      name: 'input_opt_verification_code',
      desc: '',
      args: [],
    );
  }

  /// `Sent to WhatsApp {phone}`
  String input_opt_verification_code_hint(String phone) {
    return Intl.message(
      'Sent to WhatsApp $phone',
      name: 'input_opt_verification_code_hint',
      desc: '提示用户验证码已发送到哪个WhatsApp号码',
      args: [phone],
    );
  }

  /// `Password Login`
  String get login_with_password {
    return Intl.message(
      'Password Login',
      name: 'login_with_password',
      desc: '',
      args: [],
    );
  }

  /// `Resend in`
  String get resend_in {
    return Intl.message('Resend in', name: 'resend_in', desc: '', args: []);
  }

  /// `s`
  String get seconds {
    return Intl.message('s', name: 'seconds', desc: '', args: []);
  }

  /// `Resend Code`
  String get resend_code {
    return Intl.message('Resend Code', name: 'resend_code', desc: '', args: []);
  }

  /// `Login`
  String get login {
    return Intl.message('Login', name: 'login', desc: '', args: []);
  }

  /// `Enter code`
  String get input_opt_verification_code_error {
    return Intl.message(
      'Enter code',
      name: 'input_opt_verification_code_error',
      desc: '',
      args: [],
    );
  }

  /// `Login Success`
  String get login_success {
    return Intl.message(
      'Login Success',
      name: 'login_success',
      desc: '',
      args: [],
    );
  }

  /// `Welcome back!`
  String get welcome_back {
    return Intl.message(
      'Welcome back!',
      name: 'welcome_back',
      desc: '',
      args: [],
    );
  }

  /// `Enter password`
  String get please_input_your_password {
    return Intl.message(
      'Enter password',
      name: 'please_input_your_password',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get login_password {
    return Intl.message('Password', name: 'login_password', desc: '', args: []);
  }

  /// `Enter password`
  String get input_password_hint {
    return Intl.message(
      'Enter password',
      name: 'input_password_hint',
      desc: '',
      args: [],
    );
  }

  /// `Code Login`
  String get login_with_verification_code {
    return Intl.message(
      'Code Login',
      name: 'login_with_verification_code',
      desc: '',
      args: [],
    );
  }

  /// `By logging in, you agree to the`
  String get login_agreement {
    return Intl.message(
      'By logging in, you agree to the',
      name: 'login_agreement',
      desc: '',
      args: [],
    );
  }

  /// `and`
  String get and {
    return Intl.message('and', name: 'and', desc: '', args: []);
  }

  /// `Terms of Service`
  String get user_agreement {
    return Intl.message(
      'Terms of Service',
      name: 'user_agreement',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacy_policy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacy_policy',
      desc: '',
      args: [],
    );
  }

  /// `Set Password`
  String get setting_login_password {
    return Intl.message(
      'Set Password',
      name: 'setting_login_password',
      desc: '',
      args: [],
    );
  }

  /// `6-20 letters + numbers`
  String get set_password_hint {
    return Intl.message(
      '6-20 letters + numbers',
      name: 'set_password_hint',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Password`
  String get login_password_confirm {
    return Intl.message(
      'Confirm Password',
      name: 'login_password_confirm',
      desc: '',
      args: [],
    );
  }

  /// `Re-enter password`
  String get login_password_confirm_hint {
    return Intl.message(
      'Re-enter password',
      name: 'login_password_confirm_hint',
      desc: '',
      args: [],
    );
  }

  /// `Passwords don't match`
  String get password_not_same {
    return Intl.message(
      'Passwords don\'t match',
      name: 'password_not_same',
      desc: '',
      args: [],
    );
  }

  /// `Updated`
  String get modify_success {
    return Intl.message('Updated', name: 'modify_success', desc: '', args: []);
  }

  /// `Found Product`
  String get find_product {
    return Intl.message(
      'Found Product',
      name: 'find_product',
      desc: '',
      args: [],
    );
  }

  /// `View Cashback`
  String get check_cash_back {
    return Intl.message(
      'View Cashback',
      name: 'check_cash_back',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get back {
    return Intl.message('Back', name: 'back', desc: '', args: []);
  }

  /// `Can't open link`
  String get can_not_open_link {
    return Intl.message(
      'Can\'t open link',
      name: 'can_not_open_link',
      desc: '',
      args: [],
    );
  }

  /// `Collection`
  String get collection {
    return Intl.message('Collection', name: 'collection', desc: '', args: []);
  }

  /// `Order Now`
  String get order_right_now {
    return Intl.message(
      'Order Now',
      name: 'order_right_now',
      desc: '',
      args: [],
    );
  }

  /// `Added to Collections`
  String get add_to_collection_success {
    return Intl.message(
      'Added to Collections',
      name: 'add_to_collection_success',
      desc: '',
      args: [],
    );
  }

  /// `Select bank/e-wallet`
  String get please_select_bank_or_e_wallet {
    return Intl.message(
      'Select bank/e-wallet',
      name: 'please_select_bank_or_e_wallet',
      desc: '',
      args: [],
    );
  }

  /// `Enter amount`
  String get please_input_amount {
    return Intl.message(
      'Enter amount',
      name: 'please_input_amount',
      desc: '',
      args: [],
    );
  }

  /// `Select bank`
  String get please_select_bank {
    return Intl.message(
      'Select bank',
      name: 'please_select_bank',
      desc: '',
      args: [],
    );
  }

  /// `Enter card number`
  String get please_input_bank_number {
    return Intl.message(
      'Enter card number',
      name: 'please_input_bank_number',
      desc: '',
      args: [],
    );
  }

  /// `Select e-wallet`
  String get please_select_e_wallet {
    return Intl.message(
      'Select e-wallet',
      name: 'please_select_e_wallet',
      desc: '',
      args: [],
    );
  }

  /// `Enter e-wallet`
  String get please_input_e_wallet_account {
    return Intl.message(
      'Enter e-wallet',
      name: 'please_input_e_wallet_account',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout {
    return Intl.message('Logout', name: 'logout', desc: '', args: []);
  }

  /// `Confirm Logout`
  String get logout_confirm_title {
    return Intl.message(
      'Confirm Logout',
      name: 'logout_confirm_title',
      desc: '',
      args: [],
    );
  }

  /// `Log out now?`
  String get logout_confirm_message {
    return Intl.message(
      'Log out now?',
      name: 'logout_confirm_message',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get confirm {
    return Intl.message('Confirm', name: 'confirm', desc: '', args: []);
  }

  /// `Will jump to TikTok`
  String get jump_to_tiktok {
    return Intl.message(
      'Will jump to TikTok',
      name: 'jump_to_tiktok',
      desc: '',
      args: [],
    );
  }

  /// `Place your order immediately through GENCO to receive an estimated cashback of Rp {amount}`
  String share_text(String amount) {
    return Intl.message(
      'Place your order immediately through GENCO to receive an estimated cashback of Rp $amount',
      name: 'share_text',
      desc: '分享产品的文本',
      args: [amount],
    );
  }

  /// `Please input nickname`
  String get nickname_hint {
    return Intl.message(
      'Please input nickname',
      name: 'nickname_hint',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade to become an agent or partner to earn more`
  String get member_introduction {
    return Intl.message(
      'Upgrade to become an agent or partner to earn more',
      name: 'member_introduction',
      desc: '',
      args: [],
    );
  }

  /// `Level Status`
  String get member_level_state {
    return Intl.message(
      'Level Status',
      name: 'member_level_state',
      desc: '',
      args: [],
    );
  }

  /// `You are not currently an agent or partner`
  String get member_status_description {
    return Intl.message(
      'You are not currently an agent or partner',
      name: 'member_status_description',
      desc: '',
      args: [],
    );
  }

  /// `Silver Agent`
  String get member_level_silver_agent {
    return Intl.message(
      'Silver Agent',
      name: 'member_level_silver_agent',
      desc: '',
      args: [],
    );
  }

  /// `Partner`
  String get member_level_partner {
    return Intl.message(
      'Partner',
      name: 'member_level_partner',
      desc: '',
      args: [],
    );
  }

  /// `Silver Agent Fee`
  String get member_level_silver_agent_fee {
    return Intl.message(
      'Silver Agent Fee',
      name: 'member_level_silver_agent_fee',
      desc: '',
      args: [],
    );
  }

  /// `Partner Fee`
  String get member_level_partner_fee {
    return Intl.message(
      'Partner Fee',
      name: 'member_level_partner_fee',
      desc: '',
      args: [],
    );
  }

  /// `Become Agent`
  String get become_member {
    return Intl.message(
      'Become Agent',
      name: 'become_member',
      desc: '',
      args: [],
    );
  }

  /// `Year`
  String get year {
    return Intl.message('Year', name: 'year', desc: '', args: []);
  }

  /// `Deactivate Account`
  String get delete_account {
    return Intl.message(
      'Deactivate Account',
      name: 'delete_account',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Account Deletion?`
  String get delete_account_title {
    return Intl.message(
      'Confirm Account Deletion?',
      name: 'delete_account_title',
      desc: '',
      args: [],
    );
  }

  /// `After account deletion, all your privileges and benefits will be permanently lost,\nand you will permanently lose access to your account.`
  String get delete_account_content {
    return Intl.message(
      'After account deletion, all your privileges and benefits will be permanently lost,\nand you will permanently lose access to your account.',
      name: 'delete_account_content',
      desc: '',
      args: [],
    );
  }

  /// `No product found for the link`
  String get product_link_empty {
    return Intl.message(
      'No product found for the link',
      name: 'product_link_empty',
      desc: '',
      args: [],
    );
  }

  /// `No matching product found for this link. Please check the link or try another product.`
  String get product_link_empty_content {
    return Intl.message(
      'No matching product found for this link. Please check the link or try another product.',
      name: 'product_link_empty_content',
      desc: '',
      args: [],
    );
  }

  /// `Exclusive Benefits`
  String get exclusive_benefits {
    return Intl.message(
      'Exclusive Benefits',
      name: 'exclusive_benefits',
      desc: '',
      args: [],
    );
  }

  /// `Validity Period`
  String get member_benefits_silver_agent_1 {
    return Intl.message(
      'Validity Period',
      name: 'member_benefits_silver_agent_1',
      desc: '',
      args: [],
    );
  }

  /// `1 Year`
  String get member_benefits_silver_agent_1_value {
    return Intl.message(
      '1 Year',
      name: 'member_benefits_silver_agent_1_value',
      desc: '',
      args: [],
    );
  }

  /// `Referral Bonus`
  String get member_benefits_silver_agent_2 {
    return Intl.message(
      'Referral Bonus',
      name: 'member_benefits_silver_agent_2',
      desc: '',
      args: [],
    );
  }

  /// `Get extra rewards for successful referrals`
  String get member_benefits_silver_agent_2_value {
    return Intl.message(
      'Get extra rewards for successful referrals',
      name: 'member_benefits_silver_agent_2_value',
      desc: '',
      args: [],
    );
  }

  /// `Team Shopping Commission`
  String get member_benefits_silver_agent_3 {
    return Intl.message(
      'Team Shopping Commission',
      name: 'member_benefits_silver_agent_3',
      desc: '',
      args: [],
    );
  }

  /// `Earn commissions from your team's purchases`
  String get member_benefits_silver_agent_3_value {
    return Intl.message(
      'Earn commissions from your team\'s purchases',
      name: 'member_benefits_silver_agent_3_value',
      desc: '',
      args: [],
    );
  }

  /// `Extra Cashback`
  String get member_benefits_silver_agent_4 {
    return Intl.message(
      'Extra Cashback',
      name: 'member_benefits_silver_agent_4',
      desc: '',
      args: [],
    );
  }

  /// `Higher cashback up to 50%`
  String get member_benefits_silver_agent_4_value {
    return Intl.message(
      'Higher cashback up to 50%',
      name: 'member_benefits_silver_agent_4_value',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited Cashback`
  String get member_benefits_silver_agent_5 {
    return Intl.message(
      'Unlimited Cashback',
      name: 'member_benefits_silver_agent_5',
      desc: '',
      args: [],
    );
  }

  /// `Cashback without limits`
  String get member_benefits_silver_agent_5_value {
    return Intl.message(
      'Cashback without limits',
      name: 'member_benefits_silver_agent_5_value',
      desc: '',
      args: [],
    );
  }

  /// `Validity Period`
  String get member_benefits_partner_agent_1 {
    return Intl.message(
      'Validity Period',
      name: 'member_benefits_partner_agent_1',
      desc: '',
      args: [],
    );
  }

  /// `Permanent`
  String get member_benefits_partner_agent_1_value {
    return Intl.message(
      'Permanent',
      name: 'member_benefits_partner_agent_1_value',
      desc: '',
      args: [],
    );
  }

  /// `Referral Bonus`
  String get member_benefits_partner_agent_2 {
    return Intl.message(
      'Referral Bonus',
      name: 'member_benefits_partner_agent_2',
      desc: '',
      args: [],
    );
  }

  /// `Receive high bonuses up to 1 billion+ upon success`
  String get member_benefits_partner_agent_2_value {
    return Intl.message(
      'Receive high bonuses up to 1 billion+ upon success',
      name: 'member_benefits_partner_agent_2_value',
      desc: '',
      args: [],
    );
  }

  /// `Team Shopping Commission`
  String get member_benefits_partner_agent_3 {
    return Intl.message(
      'Team Shopping Commission',
      name: 'member_benefits_partner_agent_3',
      desc: '',
      args: [],
    );
  }

  /// `Earn up to 20% cashback from each downline's cashback`
  String get member_benefits_partner_agent_3_value {
    return Intl.message(
      'Earn up to 20% cashback from each downline\'s cashback',
      name: 'member_benefits_partner_agent_3_value',
      desc: '',
      args: [],
    );
  }

  /// `Extra Cashback`
  String get member_benefits_partner_agent_4 {
    return Intl.message(
      'Extra Cashback',
      name: 'member_benefits_partner_agent_4',
      desc: '',
      args: [],
    );
  }

  /// `Higher cashback up to 100%`
  String get member_benefits_partner_agent_4_value {
    return Intl.message(
      'Higher cashback up to 100%',
      name: 'member_benefits_partner_agent_4_value',
      desc: '',
      args: [],
    );
  }

  /// `Role`
  String get role {
    return Intl.message('Role', name: 'role', desc: '', args: []);
  }

  /// `Benefit`
  String get benefit {
    return Intl.message('Benefit', name: 'benefit', desc: '', args: []);
  }

  /// `Regular User`
  String get normal_user {
    return Intl.message(
      'Regular User',
      name: 'normal_user',
      desc: '',
      args: [],
    );
  }

  /// `No bonus`
  String get normal_user_2_benefit {
    return Intl.message(
      'No bonus',
      name: 'normal_user_2_benefit',
      desc: '',
      args: [],
    );
  }

  /// `Agent`
  String get agent {
    return Intl.message('Agent', name: 'agent', desc: '', args: []);
  }

  /// `Can earn up to 10,000,000 IDR bonus`
  String get member_benefits_silver_agent_2_benefit {
    return Intl.message(
      'Can earn up to 10,000,000 IDR bonus',
      name: 'member_benefits_silver_agent_2_benefit',
      desc: '',
      args: [],
    );
  }

  /// `No shopping commission`
  String get normal_user_3_benefit {
    return Intl.message(
      'No shopping commission',
      name: 'normal_user_3_benefit',
      desc: '',
      args: [],
    );
  }

  /// `10% commission from direct referrals' shopping cashback (5% regular + 5% promotion)`
  String get member_benefits_silver_agent_3_benefit {
    return Intl.message(
      '10% commission from direct referrals\' shopping cashback (5% regular + 5% promotion)',
      name: 'member_benefits_silver_agent_3_benefit',
      desc: '',
      args: [],
    );
  }

  /// `No shopping commission`
  String get normal_user_4_benefit {
    return Intl.message(
      'No shopping commission',
      name: 'normal_user_4_benefit',
      desc: '',
      args: [],
    );
  }

  /// `10% commission from direct referrals' shopping cashback (5% regular + 5% promotion)`
  String get member_benefits_silver_agent_4_benefit {
    return Intl.message(
      '10% commission from direct referrals\' shopping cashback (5% regular + 5% promotion)',
      name: 'member_benefits_silver_agent_4_benefit',
      desc: '',
      args: [],
    );
  }

  /// `No shopping commission`
  String get normal_user_5_benefit {
    return Intl.message(
      'No shopping commission',
      name: 'normal_user_5_benefit',
      desc: '',
      args: [],
    );
  }

  /// `10% commission from direct referrals' shopping cashback (5% regular + 5% promotion)`
  String get member_benefits_silver_agent_5_benefit {
    return Intl.message(
      '10% commission from direct referrals\' shopping cashback (5% regular + 5% promotion)',
      name: 'member_benefits_silver_agent_5_benefit',
      desc: '',
      args: [],
    );
  }

  /// `Order Payment`
  String get order_payment {
    return Intl.message(
      'Order Payment',
      name: 'order_payment',
      desc: '',
      args: [],
    );
  }

  /// `Order Amount`
  String get order_price {
    return Intl.message(
      'Order Amount',
      name: 'order_price',
      desc: '',
      args: [],
    );
  }

  /// `Product Name`
  String get product_name {
    return Intl.message(
      'Product Name',
      name: 'product_name',
      desc: '',
      args: [],
    );
  }

  /// `Actual Payment Amount`
  String get real_payment_price {
    return Intl.message(
      'Actual Payment Amount',
      name: 'real_payment_price',
      desc: '',
      args: [],
    );
  }

  /// `Agent Fee`
  String get agent_fee {
    return Intl.message('Agent Fee', name: 'agent_fee', desc: '', args: []);
  }

  /// `Pay Now`
  String get purchase_right_now {
    return Intl.message(
      'Pay Now',
      name: 'purchase_right_now',
      desc: '',
      args: [],
    );
  }

  /// `Payment Issue`
  String get payment_problem {
    return Intl.message(
      'Payment Issue',
      name: 'payment_problem',
      desc: '',
      args: [],
    );
  }

  /// `Completed`
  String get payment_complete {
    return Intl.message(
      'Completed',
      name: 'payment_complete',
      desc: '',
      args: [],
    );
  }

  /// `Read and Agree`
  String get payment_agreement {
    return Intl.message(
      'Read and Agree',
      name: 'payment_agreement',
      desc: '',
      args: [],
    );
  }

  /// `Payment Terms`
  String get payment_agreement_link {
    return Intl.message(
      'Payment Terms',
      name: 'payment_agreement_link',
      desc: '',
      args: [],
    );
  }

  /// `This product has no cashback`
  String get cashback_is_0 {
    return Intl.message(
      'This product has no cashback',
      name: 'cashback_is_0',
      desc: '',
      args: [],
    );
  }

  /// `There is no cashback for this product. Do you want to continue?`
  String get cashback_is_0_content {
    return Intl.message(
      'There is no cashback for this product. Do you want to continue?',
      name: 'cashback_is_0_content',
      desc: '',
      args: [],
    );
  }

  /// `Nickname is too long, maximum 10 characters`
  String get nickname_too_long {
    return Intl.message(
      'Nickname is too long, maximum 10 characters',
      name: 'nickname_too_long',
      desc: '',
      args: [],
    );
  }

  /// `Checking Payment Result`
  String get check_payment_result {
    return Intl.message(
      'Checking Payment Result',
      name: 'check_payment_result',
      desc: '',
      args: [],
    );
  }

  /// `Payment Amount`
  String get payment_amount {
    return Intl.message(
      'Payment Amount',
      name: 'payment_amount',
      desc: '',
      args: [],
    );
  }

  /// `Payment Transaction ID`
  String get payment_id {
    return Intl.message(
      'Payment Transaction ID',
      name: 'payment_id',
      desc: '',
      args: [],
    );
  }

  /// `Payment Method`
  String get payment_method {
    return Intl.message(
      'Payment Method',
      name: 'payment_method',
      desc: '',
      args: [],
    );
  }

  /// `Payment Successful`
  String get payment_success {
    return Intl.message(
      'Payment Successful',
      name: 'payment_success',
      desc: '',
      args: [],
    );
  }

  /// `Payment Failed`
  String get payment_failed {
    return Intl.message(
      'Payment Failed',
      name: 'payment_failed',
      desc: '',
      args: [],
    );
  }

  /// `Level Status`
  String get level_status {
    return Intl.message(
      'Level Status',
      name: 'level_status',
      desc: '',
      args: [],
    );
  }

  /// `Valid for: 1 year`
  String get valid_for {
    return Intl.message(
      'Valid for: 1 year',
      name: 'valid_for',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade Date: June 11, 2025`
  String get upgrade_date {
    return Intl.message(
      'Upgrade Date: June 11, 2025',
      name: 'upgrade_date',
      desc: '',
      args: [],
    );
  }

  /// `Progress to Gold Agent`
  String get to_gold_progress {
    return Intl.message(
      'Progress to Gold Agent',
      name: 'to_gold_progress',
      desc: '',
      args: [],
    );
  }

  /// `Invite 10 friends to become Silver Agents or higher`
  String get invite_to_upgrade {
    return Intl.message(
      'Invite 10 friends to become Silver Agents or higher',
      name: 'invite_to_upgrade',
      desc: '',
      args: [],
    );
  }

  /// `Silver Agent`
  String get silver_agent {
    return Intl.message(
      'Silver Agent',
      name: 'silver_agent',
      desc: '',
      args: [],
    );
  }

  /// `Gold Agent`
  String get gold_agent {
    return Intl.message('Gold Agent', name: 'gold_agent', desc: '', args: []);
  }

  /// `Diamond Agent`
  String get diamond_agent {
    return Intl.message(
      'Diamond Agent',
      name: 'diamond_agent',
      desc: '',
      args: [],
    );
  }

  /// `Partner`
  String get partner {
    return Intl.message('Partner', name: 'partner', desc: '', args: []);
  }

  /// `Direct Invitation Reward`
  String get direct_invite_reward {
    return Intl.message(
      'Direct Invitation Reward',
      name: 'direct_invite_reward',
      desc: '',
      args: [],
    );
  }

  /// `Invite 1 agent to get 35,000 IDR reward. Invite 3 to break even!`
  String get direct_invite_detail {
    return Intl.message(
      'Invite 1 agent to get 35,000 IDR reward. Invite 3 to break even!',
      name: 'direct_invite_detail',
      desc: '',
      args: [],
    );
  }

  /// `Team Purchase Commission`
  String get team_purchase_bonus {
    return Intl.message(
      'Team Purchase Commission',
      name: 'team_purchase_bonus',
      desc: '',
      args: [],
    );
  }

  /// `Earn 10% commission on shopping of your directly invited team`
  String get team_purchase_detail {
    return Intl.message(
      'Earn 10% commission on shopping of your directly invited team',
      name: 'team_purchase_detail',
      desc: '',
      args: [],
    );
  }

  /// `Indirect invitation (level 2): Each accumulates 15,000 IDR; Indirect invitation (level 3): Each accumulates 10,000 IDR.`
  String get team_purchase_detail_gold {
    return Intl.message(
      'Indirect invitation (level 2): Each accumulates 15,000 IDR; Indirect invitation (level 3): Each accumulates 10,000 IDR.',
      name: 'team_purchase_detail_gold',
      desc: '',
      args: [],
    );
  }

  /// `Training`
  String get training {
    return Intl.message('Training', name: 'training', desc: '', args: []);
  }

  /// `Professional mentors provide high-quality courses and guidance`
  String get training_detail {
    return Intl.message(
      'Professional mentors provide high-quality courses and guidance',
      name: 'training_detail',
      desc: '',
      args: [],
    );
  }

  /// `Extra Cashback`
  String get extra_cashback {
    return Intl.message(
      'Extra Cashback',
      name: 'extra_cashback',
      desc: '',
      args: [],
    );
  }

  /// `For every 10 Gold Agents developed: 300,000 IDR reward`
  String get extra_cashback_detail_gold {
    return Intl.message(
      'For every 10 Gold Agents developed: 300,000 IDR reward',
      name: 'extra_cashback_detail_gold',
      desc: '',
      args: [],
    );
  }

  /// `Enjoy extra cashback benefits during specified periods`
  String get extra_cashback_detail {
    return Intl.message(
      'Enjoy extra cashback benefits during specified periods',
      name: 'extra_cashback_detail',
      desc: '',
      args: [],
    );
  }

  /// `Invite 10 new friends to become Silver Agents or higher.\nYour status will upgrade automatically!`
  String get invite_to_upgrade_empty {
    return Intl.message(
      'Invite 10 new friends to become Silver Agents or higher.\\nYour status will upgrade automatically!',
      name: 'invite_to_upgrade_empty',
      desc: '',
      args: [],
    );
  }

  /// `Silver Partner`
  String get silver_partner {
    return Intl.message(
      'Silver Partner',
      name: 'silver_partner',
      desc: '',
      args: [],
    );
  }

  /// `Gold Partner`
  String get gold_partner {
    return Intl.message(
      'Gold Partner',
      name: 'gold_partner',
      desc: '',
      args: [],
    );
  }

  /// `Diamond Partner`
  String get diamond_partner {
    return Intl.message(
      'Diamond Partner',
      name: 'diamond_partner',
      desc: '',
      args: [],
    );
  }

  /// `For every 10 Silver Partners developed: 1,000,000 IDR reward`
  String get partner_extra_bonus1 {
    return Intl.message(
      'For every 10 Silver Partners developed: 1,000,000 IDR reward',
      name: 'partner_extra_bonus1',
      desc: '',
      args: [],
    );
  }

  /// `For every 10 Gold Partners developed: 2,000,000 IDR reward`
  String get partner_extra_bonus2 {
    return Intl.message(
      'For every 10 Gold Partners developed: 2,000,000 IDR reward',
      name: 'partner_extra_bonus2',
      desc: '',
      args: [],
    );
  }

  /// `Reward 200,000 IDR per agent invited, break even by inviting just 3!`
  String get direct_invite_detail2 {
    return Intl.message(
      'Reward 200,000 IDR per agent invited, break even by inviting just 3!',
      name: 'direct_invite_detail2',
      desc: '',
      args: [],
    );
  }

  /// `Indirect invitation (level 2): Each gets 100,000 IDR; Indirect invitation (level 3): Each gets 50,000 IDR`
  String get team_purchase_detail_gold2 {
    return Intl.message(
      'Indirect invitation (level 2): Each gets 100,000 IDR; Indirect invitation (level 3): Each gets 50,000 IDR',
      name: 'team_purchase_detail_gold2',
      desc: '',
      args: [],
    );
  }

  /// `Extra Bonus`
  String get extra_bonus {
    return Intl.message('Extra Bonus', name: 'extra_bonus', desc: '', args: []);
  }

  /// `Regular Member`
  String get normal_member {
    return Intl.message(
      'Regular Member',
      name: 'normal_member',
      desc: '',
      args: [],
    );
  }

  /// `High Cashback`
  String get high_cashback {
    return Intl.message(
      'High Cashback',
      name: 'high_cashback',
      desc: '',
      args: [],
    );
  }

  /// `Spend more, save more`
  String get high_cashback_description {
    return Intl.message(
      'Spend more, save more',
      name: 'high_cashback_description',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited`
  String get no_limit {
    return Intl.message('Unlimited', name: 'no_limit', desc: '', args: []);
  }

  /// `Enjoy unlimited cashback`
  String get no_limit_description {
    return Intl.message(
      'Enjoy unlimited cashback',
      name: 'no_limit_description',
      desc: '',
      args: [],
    );
  }

  /// `Customer Service`
  String get user_service {
    return Intl.message(
      'Customer Service',
      name: 'user_service',
      desc: '',
      args: [],
    );
  }

  /// `Premium customer service`
  String get user_service_description {
    return Intl.message(
      'Premium customer service',
      name: 'user_service_description',
      desc: '',
      args: [],
    );
  }

  /// `Invite and Earn`
  String get invite_and_eran_bonus {
    return Intl.message(
      'Invite and Earn',
      name: 'invite_and_eran_bonus',
      desc: '',
      args: [],
    );
  }

  /// `Invitation Code`
  String get invite_code {
    return Intl.message(
      'Invitation Code',
      name: 'invite_code',
      desc: '',
      args: [],
    );
  }

  /// `Enter Invitation Code`
  String get input_invite_code {
    return Intl.message(
      'Enter Invitation Code',
      name: 'input_invite_code',
      desc: '',
      args: [],
    );
  }

  /// `Contact Supervisor`
  String get contact_up {
    return Intl.message(
      'Contact Supervisor',
      name: 'contact_up',
      desc: '',
      args: [],
    );
  }

  /// `Congratulations on joining`
  String get congratulation_to_add_group {
    return Intl.message(
      'Congratulations on joining',
      name: 'congratulation_to_add_group',
      desc: '',
      args: [],
    );
  }

  /// `'s team`
  String get group {
    return Intl.message('\'s team', name: 'group', desc: '', args: []);
  }

  /// `My Team`
  String get my_team {
    return Intl.message('My Team', name: 'my_team', desc: '', args: []);
  }

  /// `Task Center`
  String get task_center {
    return Intl.message('Task Center', name: 'task_center', desc: '', args: []);
  }

  /// `Task Center`
  String get task_center_title {
    return Intl.message(
      'Task Center',
      name: 'task_center_title',
      desc: '',
      args: [],
    );
  }

  /// `Cash Income (Rp)`
  String get task_cash_income {
    return Intl.message(
      'Cash Income (Rp)',
      name: 'task_cash_income',
      desc: '',
      args: [],
    );
  }

  /// `Withdraw`
  String get task_withdraw {
    return Intl.message('Withdraw', name: 'task_withdraw', desc: '', args: []);
  }

  /// `Withdrawable Amount`
  String get task_withdrawable_amount {
    return Intl.message(
      'Withdrawable Amount',
      name: 'task_withdrawable_amount',
      desc: '',
      args: [],
    );
  }

  /// `Task List`
  String get task_daily_tasks {
    return Intl.message(
      'Task List',
      name: 'task_daily_tasks',
      desc: '',
      args: [],
    );
  }

  /// `Invite First Order Reward`
  String get task_invite_reward {
    return Intl.message(
      'Invite First Order Reward',
      name: 'task_invite_reward',
      desc: '',
      args: [],
    );
  }

  /// `Invite Progress`
  String get task_invite_progress {
    return Intl.message(
      'Invite Progress',
      name: 'task_invite_progress',
      desc: '',
      args: [],
    );
  }

  /// `Order Progress`
  String get task_order_progress {
    return Intl.message(
      'Order Progress',
      name: 'task_order_progress',
      desc: '',
      args: [],
    );
  }

  /// `Invite Count`
  String get task_invite_count {
    return Intl.message(
      'Invite Count',
      name: 'task_invite_count',
      desc: '',
      args: [],
    );
  }

  /// `Order Count`
  String get task_order_count {
    return Intl.message(
      'Order Count',
      name: 'task_order_count',
      desc: '',
      args: [],
    );
  }

  /// `Conditions Met`
  String get task_conditions_met {
    return Intl.message(
      'Conditions Met',
      name: 'task_conditions_met',
      desc: '',
      args: [],
    );
  }

  /// `Conditions Not Met`
  String get task_conditions_not_met {
    return Intl.message(
      'Conditions Not Met',
      name: 'task_conditions_not_met',
      desc: '',
      args: [],
    );
  }

  /// `Claim`
  String get task_go_claim {
    return Intl.message('Claim', name: 'task_go_claim', desc: '', args: []);
  }

  /// `Feature Developing`
  String get task_feature_developing {
    return Intl.message(
      'Feature Developing',
      name: 'task_feature_developing',
      desc: '',
      args: [],
    );
  }

  /// `Task Feature Developing`
  String get task_developing {
    return Intl.message(
      'Task Feature Developing',
      name: 'task_developing',
      desc: '',
      args: [],
    );
  }

  /// `Return Cash Welfare`
  String get task_return_cash_welfare {
    return Intl.message(
      'Return Cash Welfare',
      name: 'task_return_cash_welfare',
      desc: '',
      args: [],
    );
  }

  /// `Daily Exclusive`
  String get task_return_cash_welfare_desc {
    return Intl.message(
      'Daily Exclusive',
      name: 'task_return_cash_welfare_desc',
      desc: '',
      args: [],
    );
  }

  /// `View Record`
  String get task_view_record {
    return Intl.message(
      'View Record',
      name: 'task_view_record',
      desc: '',
      args: [],
    );
  }

  /// `Task Record`
  String get task_record_title {
    return Intl.message(
      'Task Record',
      name: 'task_record_title',
      desc: '',
      args: [],
    );
  }

  /// `Total Invites`
  String get task_total_invites {
    return Intl.message(
      'Total Invites',
      name: 'task_total_invites',
      desc: '',
      args: [],
    );
  }

  /// `Redeemed Invites`
  String get task_redeemed_invites {
    return Intl.message(
      'Redeemed Invites',
      name: 'task_redeemed_invites',
      desc: '',
      args: [],
    );
  }

  /// `Total Orders`
  String get task_total_orders {
    return Intl.message(
      'Total Orders',
      name: 'task_total_orders',
      desc: '',
      args: [],
    );
  }

  /// `Redeemed Orders`
  String get task_redeemed_orders {
    return Intl.message(
      'Redeemed Orders',
      name: 'task_redeemed_orders',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get task_close {
    return Intl.message('Close', name: 'task_close', desc: '', args: []);
  }

  /// `Reward Amount`
  String get task_reward_amount {
    return Intl.message(
      'Reward Amount',
      name: 'task_reward_amount',
      desc: '',
      args: [],
    );
  }

  /// `Per Completion`
  String get task_per_completion {
    return Intl.message(
      'Per Completion',
      name: 'task_per_completion',
      desc: '',
      args: [],
    );
  }

  /// `Referral Bonus`
  String get invite_bonus {
    return Intl.message(
      'Referral Bonus',
      name: 'invite_bonus',
      desc: '',
      args: [],
    );
  }

  /// `Shopping Bonus`
  String get shopping_bonus {
    return Intl.message(
      'Shopping Bonus',
      name: 'shopping_bonus',
      desc: '',
      args: [],
    );
  }

  /// `Total Invitations`
  String get cumulative_number_of_invitations {
    return Intl.message(
      'Total Invitations',
      name: 'cumulative_number_of_invitations',
      desc: '',
      args: [],
    );
  }

  /// `Today`
  String get today {
    return Intl.message('Today', name: 'today', desc: '', args: []);
  }

  /// `Invite Agent`
  String get invite_agent {
    return Intl.message(
      'Invite Agent',
      name: 'invite_agent',
      desc: '',
      args: [],
    );
  }

  /// `Invite Regular User`
  String get invite_normal_user {
    return Intl.message(
      'Invite Regular User',
      name: 'invite_normal_user',
      desc: '',
      args: [],
    );
  }

  /// `Silver`
  String get silver {
    return Intl.message('Silver', name: 'silver', desc: '', args: []);
  }

  /// `Gold`
  String get gold {
    return Intl.message('Gold', name: 'gold', desc: '', args: []);
  }

  /// `Diamond`
  String get diamond {
    return Intl.message('Diamond', name: 'diamond', desc: '', args: []);
  }

  /// `Team Contribution`
  String get team_support {
    return Intl.message(
      'Team Contribution',
      name: 'team_support',
      desc: '',
      args: [],
    );
  }

  /// `Received Bonus`
  String get received_bonus {
    return Intl.message(
      'Received Bonus',
      name: 'received_bonus',
      desc: '',
      args: [],
    );
  }

  /// `Invitation Time`
  String get invite_time {
    return Intl.message(
      'Invitation Time',
      name: 'invite_time',
      desc: '',
      args: [],
    );
  }

  /// `Estimated Team Contribution Cashback`
  String get pre_team_cashback {
    return Intl.message(
      'Estimated Team Contribution Cashback',
      name: 'pre_team_cashback',
      desc: '',
      args: [],
    );
  }

  /// `Invite and Earn Money`
  String get invite_and_earn_money {
    return Intl.message(
      'Invite and Earn Money',
      name: 'invite_and_earn_money',
      desc: '',
      args: [],
    );
  }

  /// `Level Up Progress`
  String get level_up_schedule {
    return Intl.message(
      'Level Up Progress',
      name: 'level_up_schedule',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade Instructions`
  String get level_up_description {
    return Intl.message(
      'Upgrade Instructions',
      name: 'level_up_description',
      desc: '',
      args: [],
    );
  }

  /// `Invite 10 friends to become Silver Agents or higher`
  String get level_up_content_title {
    return Intl.message(
      'Invite 10 friends to become Silver Agents or higher',
      name: 'level_up_content_title',
      desc: '',
      args: [],
    );
  }

  /// `IDR in Hand`
  String get level_up_description_title {
    return Intl.message(
      'IDR in Hand',
      name: 'level_up_description_title',
      desc: '',
      args: [],
    );
  }

  /// `Just develop 10 Diamond Agents!`
  String get level_up_description_title1 {
    return Intl.message(
      'Just develop 10 Diamond Agents!',
      name: 'level_up_description_title1',
      desc: '',
      args: [],
    );
  }

  /// `Invite Gold Agent`
  String get invite_gold_agent {
    return Intl.message(
      'Invite Gold Agent',
      name: 'invite_gold_agent',
      desc: '',
      args: [],
    );
  }

  /// `Invite Diamond Agent`
  String get invite_diamond_agent {
    return Intl.message(
      'Invite Diamond Agent',
      name: 'invite_diamond_agent',
      desc: '',
      args: [],
    );
  }

  /// `Level Up Progress & Rewards`
  String get level_up_bonus {
    return Intl.message(
      'Level Up Progress & Rewards',
      name: 'level_up_bonus',
      desc: '',
      args: [],
    );
  }

  /// `Activity Rules`
  String get activity_rule {
    return Intl.message(
      'Activity Rules',
      name: 'activity_rule',
      desc: '',
      args: [],
    );
  }

  /// `Reward`
  String get bonus {
    return Intl.message('Reward', name: 'bonus', desc: '', args: []);
  }

  /// `Invite agents: 35,000 IDR reward per person`
  String get direct_invite_detail3 {
    return Intl.message(
      'Invite agents: 35,000 IDR reward per person',
      name: 'direct_invite_detail3',
      desc: '',
      args: [],
    );
  }

  /// `Team Bonus`
  String get team_bonus {
    return Intl.message('Team Bonus', name: 'team_bonus', desc: '', args: []);
  }

  /// `Indirect referral (level 2): Each accumulates 100,000 IDR;\nIndirect referral (level 3): Each accumulates 50,000 IDR;\nIf you reach "Gold Partner" status (successfully refer 10 partners directly), bonus becomes withdrawable. Valid for 60 days from generation. If not achieved, bonus expires.`
  String get team_bonus_detail {
    return Intl.message(
      'Indirect referral (level 2): Each accumulates 100,000 IDR;\nIndirect referral (level 3): Each accumulates 50,000 IDR;\nIf you reach "Gold Partner" status (successfully refer 10 partners directly), bonus becomes withdrawable. Valid for 60 days from generation. If not achieved, bonus expires.',
      name: 'team_bonus_detail',
      desc: '',
      args: [],
    );
  }

  /// `For every 10 Gold Partners developed: 2,000,000 IDR bonus;\nFor every 10 Diamond Partners developed: 100,000,000 IDR bonus.`
  String get partner_extra_bonus3 {
    return Intl.message(
      'For every 10 Gold Partners developed: 2,000,000 IDR bonus;\nFor every 10 Diamond Partners developed: 100,000,000 IDR bonus.',
      name: 'partner_extra_bonus3',
      desc: '',
      args: [],
    );
  }

  /// `Invitation code cannot be empty!`
  String get invite_code_empty_hint {
    return Intl.message(
      'Invitation code cannot be empty!',
      name: 'invite_code_empty_hint',
      desc: '',
      args: [],
    );
  }

  /// `Copied Successfully`
  String get copy_success {
    return Intl.message(
      'Copied Successfully',
      name: 'copy_success',
      desc: '',
      args: [],
    );
  }

  /// `Network is unavailable, please check your connection`
  String get network_is_not_available {
    return Intl.message(
      'Network is unavailable, please check your connection',
      name: 'network_is_not_available',
      desc: '',
      args: [],
    );
  }

  /// `Login with other method`
  String get login_with_other_method {
    return Intl.message(
      'Login with other method',
      name: 'login_with_other_method',
      desc: '',
      args: [],
    );
  }

  /// `Silver`
  String get member_introduction_level_silver_agent {
    return Intl.message(
      'Silver',
      name: 'member_introduction_level_silver_agent',
      desc: '',
      args: [],
    );
  }

  /// `Normal`
  String get normal_member_user {
    return Intl.message(
      'Normal',
      name: 'normal_member_user',
      desc: '',
      args: [],
    );
  }

  /// `Do you agree with the payment terms?`
  String get agree_with_payment_term {
    return Intl.message(
      'Do you agree with the payment terms?',
      name: 'agree_with_payment_term',
      desc: '',
      args: [],
    );
  }

  /// `Please select a payment method`
  String get please_choose_payment_method {
    return Intl.message(
      'Please select a payment method',
      name: 'please_choose_payment_method',
      desc: '',
      args: [],
    );
  }

  /// `QRCode`
  String get qrcode {
    return Intl.message('QRCode', name: 'qrcode', desc: '', args: []);
  }

  /// `Open Payment Link Directly`
  String get open_payment_link {
    return Intl.message(
      'Open Payment Link Directly',
      name: 'open_payment_link',
      desc: '',
      args: [],
    );
  }

  /// `Pay with QR Code`
  String get pay_with_qrcode {
    return Intl.message(
      'Pay with QR Code',
      name: 'pay_with_qrcode',
      desc: '',
      args: [],
    );
  }

  /// `Scan the QR code to open the payment link and pay. If you want to pay directly in this app, tap to open the link.`
  String get pay_with_qrcode_usage {
    return Intl.message(
      'Scan the QR code to open the payment link and pay. If you want to pay directly in this app, tap to open the link.',
      name: 'pay_with_qrcode_usage',
      desc: '',
      args: [],
    );
  }

  /// `Jump Link Failed`
  String get jump_link_failed {
    return Intl.message(
      'Jump Link Failed',
      name: 'jump_link_failed',
      desc: '',
      args: [],
    );
  }

  /// `Login has expired, please log in again!`
  String get login_expired_hint {
    return Intl.message(
      'Login has expired, please log in again!',
      name: 'login_expired_hint',
      desc: '',
      args: [],
    );
  }

  /// `Network error`
  String get network_error {
    return Intl.message(
      'Network error',
      name: 'network_error',
      desc: '',
      args: [],
    );
  }

  /// `Unknown error`
  String get unknown_error {
    return Intl.message(
      'Unknown error',
      name: 'unknown_error',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'id'),
      Locale.fromSubtags(languageCode: 'zh'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
