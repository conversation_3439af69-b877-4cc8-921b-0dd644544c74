package com.genco.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.genco.common.model.brand.StoreBrand;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.page.CommonPage;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.StoreBrandUpdateRequest;
import com.genco.common.response.BrandUpdateResult;
import com.genco.common.response.StoreBrandResponse;
import com.genco.common.utils.RedisUtil;
import com.genco.service.service.BrandService;
import com.genco.service.service.StoreBrandService;
import com.genco.service.service.StoreProductService;
import com.genco.service.service.SystemConfigService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * BrandServiceImpl 实现
 */
@Service
public class BrandServiceImpl implements BrandService {

    @Autowired
    private StoreBrandService storeBrandService;

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取首页品牌列表
     *
     * @return CommonPage<StoreBrandResponse>
     */
    @Override
    public CommonPage<StoreBrandResponse> getList(Integer type, String name, PageParamRequest pageParamRequest) {
        List<StoreBrand> storeBrandList = storeBrandService.getBrandList(type, name, pageParamRequest);
        if (CollUtil.isEmpty(storeBrandList)) {
            return CommonPage.restPage(new ArrayList<>());
        }
        CommonPage<StoreBrand> storeProductCommonPage = CommonPage.restPage(storeBrandList);
        List<StoreBrandResponse> productResponseArrayList = new ArrayList<>();
        for (StoreBrand storeBrand : storeBrandList) {
            StoreBrandResponse productResponse = new StoreBrandResponse();
            BeanUtils.copyProperties(storeBrand, productResponse);

            // 查询品牌关联的商品数量
            LambdaQueryWrapper<StoreProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreProduct::getBrand, storeBrand.getCode());
            queryWrapper.eq(StoreProduct::getIsDel, false);
            queryWrapper.eq(StoreProduct::getIsRecycle, false);
            queryWrapper.eq(StoreProduct::getIsShow, true);
            int productCount = storeProductService.count(queryWrapper);
            productResponse.setProductCount(productCount);

            productResponseArrayList.add(productResponse);
        }
        CommonPage<StoreBrandResponse> brandResponseCommonPage = CommonPage.restPage(productResponseArrayList);
        BeanUtils.copyProperties(storeProductCommonPage, brandResponseCommonPage, "list");

        return brandResponseCommonPage;
    }

    @Override
    public StoreBrandResponse getDetail(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        StoreBrandResponse storeBrandResponse = new StoreBrandResponse();
        StoreBrand storeBrand = storeBrandService.getByBrandCode(code);
        BeanUtils.copyProperties(storeBrand, storeBrandResponse);

        // 查询品牌关联的商品数量
        LambdaQueryWrapper<StoreProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreProduct::getBrand, code);
        queryWrapper.eq(StoreProduct::getIsDel, false);
        queryWrapper.eq(StoreProduct::getIsRecycle, false);
        queryWrapper.eq(StoreProduct::getIsShow, true);
        int productCount = storeProductService.count(queryWrapper);
        storeBrandResponse.setProductCount(productCount);

        return storeBrandResponse;
    }

    /**
     * 更新品牌信息
     *
     * @param request 品牌更新请求对象
     * @return 是否更新成功
     */
    @Override
    public Boolean updateBrand(StoreBrandUpdateRequest request) {
        return storeBrandService.updateBrand(request);
    }

    /**
     * 批量更新品牌信息
     *
     * @param requests 品牌更新请求对象列表
     * @return List<BrandUpdateResult>
     */
    @Override
    public List<BrandUpdateResult> batchUpdateBrand(List<StoreBrandUpdateRequest> requests) {
        List<BrandUpdateResult> results = new ArrayList<>();
        if (requests == null || requests.isEmpty()) {
            return results;
        }
        for (StoreBrandUpdateRequest request : requests) {
            BrandUpdateResult result = new BrandUpdateResult();
            result.setId(request.getId());
            try {
                boolean success = storeBrandService.updateBrand(request);
                result.setSuccess(success);
                result.setMessage(success ? "更新成功" : "更新失败");
            } catch (Exception e) {
                result.setSuccess(false);
                result.setMessage(e.getMessage());
            }
            results.add(result);
        }
        return results;
    }

    @Override
    public Integer addBrand(StoreBrandUpdateRequest request) {
        return storeBrandService.addBrand(request);
    }

    @Override
    public StoreBrandResponse getById(Integer id) {
        StoreBrand storeBrand = storeBrandService.getById(id);
        if (storeBrand == null) {
            return null;
        }
        StoreBrandResponse response = new StoreBrandResponse();
        org.springframework.beans.BeanUtils.copyProperties(storeBrand, response);

        // 查询品牌关联的商品数量
        LambdaQueryWrapper<StoreProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreProduct::getBrand, storeBrand.getCode());
        queryWrapper.eq(StoreProduct::getIsDel, false);
        queryWrapper.eq(StoreProduct::getIsRecycle, false);
        queryWrapper.eq(StoreProduct::getIsShow, true);
        int productCount = storeProductService.count(queryWrapper);
        response.setProductCount(productCount);

        return response;
    }

    @Override
    public Boolean deleteBrand(Integer id) {
        return storeBrandService.deleteBrand(id);
    }
} 