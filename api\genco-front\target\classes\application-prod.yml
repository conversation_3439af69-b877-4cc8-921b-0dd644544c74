# CRMEB 相关配置
crmeb:
  version: GENCO-JAVA-KY-v1.3.4 # 当前代码版本

server:
  port: 8081

spring:
  profiles:
    #  配置的环境
    active: prod
    #  数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: jdbc:mysql://*************:53306/genco_data?useUnicode=true&allowPublicKeyRetrieval=true&characterEncoding=utf-8&useSSL=false&serverTimeZone=GMT+8
    username: root
    password: mysql-develop
  redis:
    host: 127.0.0.1 #地址
    port: 6379 #端口
    password:
    timeout: 10000 # 连接超时时间（毫秒）
    database: 0 #默认数据库
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
        time-between-eviction-runs: -1 #逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1

debug: true
logging:
  level:
    io.swagger.*: error
    com.zbjk.crmeb: debug
    org.springframework.boot.autoconfigure: ERROR
  config: classpath:logback-spring.xml
  file:
    path: ./genco_log

# mybatis 配置
mybatis-plus:
  # 配置slq打印日志
  configuration:
    log-impl:

#swagger 配置
swagger:
  basic:
    enable: true #是否开启界面
    check: true #是否打开验证
    username:  #访问swagger的账号
    password:  #访问swagger的密码
