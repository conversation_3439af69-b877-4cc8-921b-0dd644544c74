package com.genco.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.model.brand.StoreBrand;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.StoreBrandUpdateRequest;
import com.genco.common.utils.BrandCodeUtils;
import com.genco.common.utils.RedisUtil;
import com.genco.service.dao.StoreBrandDao;
import com.genco.service.service.StoreBrandService;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * StoreBrandServiceImpl 接口实现
 */
@Service
public class StoreBrandServiceImpl extends ServiceImpl<StoreBrandDao, StoreBrand> implements StoreBrandService {

    @Resource
    private StoreBrandDao dao;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取品牌列表
     *
     * @return 列表
     */
    @Override
    public List<StoreBrand> getBrandList(Integer type, String keywords, PageParamRequest pageRequest) {
        LambdaQueryWrapper<StoreBrand> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (type == 0) {
            lambdaQueryWrapper.eq(StoreBrand::getIsHot, true);
        } else if (type == 1) {
            lambdaQueryWrapper.eq(StoreBrand::getIsHighCashback, true);
        }
        if (StringUtils.isNotEmpty(keywords)) {
            lambdaQueryWrapper.apply("LOWER(name) LIKE {0}", "%" + keywords.toLowerCase() + "%");
        }
        lambdaQueryWrapper.eq(StoreBrand::getStatus, 1);
        lambdaQueryWrapper.orderByDesc(StoreBrand::getPriority);
        PageHelper.startPage(pageRequest.getPage(), pageRequest.getLimit());
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据品牌编码获取详情
     *
     * @param code 品牌编码
     * @return StoreBrand
     */
    @Override
    public StoreBrand getByBrandCode(String code) {
        LambdaQueryWrapper<StoreBrand> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreBrand::getCode, code);
        return dao.selectOne(lambdaQueryWrapper);
    }

    /**
     * 更新品牌信息
     *
     * @param request 品牌更新请求对象
     * @return 是否更新成功
     */
    @Override
    public Boolean updateBrand(StoreBrandUpdateRequest request) {
        StoreBrand brand = getById(request.getId());
        if (brand == null) {
            throw new RuntimeException("品牌不存在");
        }
        // 逐字段更新
        if (request.getCode() != null) brand.setCode(request.getCode());
        if (request.getName() != null) brand.setName(request.getName());
        if (request.getLogoUrl() != null) brand.setLogoUrl(request.getLogoUrl());
        if (request.getDescription() != null) brand.setDescription(request.getDescription());
        if (request.getIndustry() != null) brand.setIndustry(request.getIndustry());
        if (request.getStatus() != null) brand.setStatus(request.getStatus());
        if (request.getPlatform() != null) brand.setPlatform(request.getPlatform());
        if (request.getProductCount() != null) brand.setProductCount(request.getProductCount());
        if (request.getProductSoldCount() != null) brand.setProductSoldCount(request.getProductSoldCount());
        if (request.getProductSoldAmount() != null) brand.setProductSoldAmount(request.getProductSoldAmount());
        if (request.getProductCashbackAmount() != null)
            brand.setProductCashbackAmount(request.getProductCashbackAmount());
        if (request.getProductShareCount() != null) brand.setProductShareCount(request.getProductShareCount());
        if (request.getCreator() != null) brand.setCreator(request.getCreator());
        if (request.getIsHot() != null) brand.setIsHot(request.getIsHot());
        if (request.getIsHighCashback() != null) brand.setIsHighCashback(request.getIsHighCashback());
        if (request.getContactPerson() != null) brand.setContactPerson(request.getContactPerson());
        if (request.getContactPhone() != null) brand.setContactPhone(request.getContactPhone());
        return updateById(brand);
    }

    @Override
    public Integer addBrand(StoreBrandUpdateRequest request) {
        StoreBrand brand = new StoreBrand();

        // 自动生成code，如果request中没有提供code或者code为空
        if (StringUtils.isEmpty(request.getCode()) && StringUtils.isNotEmpty(request.getName())) {
            String generatedCode = BrandCodeUtils.generateBrandCode(request.getName());
            brand.setCode(generatedCode);
        } else if (request.getCode() != null) {
            brand.setCode(request.getCode());
        }
        //默认是初始状态
        brand.setStatus("0");
        if (request.getName() != null) brand.setName(request.getName());
        if (request.getLogoUrl() != null) brand.setLogoUrl(request.getLogoUrl());
        if (request.getDescription() != null) brand.setDescription(request.getDescription());
        if (request.getIndustry() != null) brand.setIndustry(request.getIndustry());
        if (request.getPlatform() != null) brand.setPlatform(request.getPlatform());
        if (request.getProductCount() != null) brand.setProductCount(request.getProductCount());
        if (request.getProductSoldCount() != null) brand.setProductSoldCount(request.getProductSoldCount());
        if (request.getProductSoldAmount() != null) brand.setProductSoldAmount(request.getProductSoldAmount());
        if (request.getProductCashbackAmount() != null)
            brand.setProductCashbackAmount(request.getProductCashbackAmount());
        if (request.getProductShareCount() != null) brand.setProductShareCount(request.getProductShareCount());
        if (request.getCreator() != null) brand.setCreator(request.getCreator());
        if (request.getIsHot() != null) brand.setIsHot(request.getIsHot());
        if (request.getIsHighCashback() != null) brand.setIsHighCashback(request.getIsHighCashback());
        if (request.getContactPerson() != null) brand.setContactPerson(request.getContactPerson());
        if (request.getContactPhone() != null) brand.setContactPhone(request.getContactPhone());
        dao.insert(brand);
        return brand.getId();
    }

    @Override
    public Boolean deleteBrand(Integer id) {
        StoreBrand brand = getById(id);
        if (brand == null) {
            throw new RuntimeException("品牌不存在");
        }
        brand.setStatus("-1");
        return updateById(brand);
    }
}

