// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a id locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'id';

  static String m0(phone) => "Kode verifikasi dikirim ke WhatsApp ${phone}";

  static String m1(amount) =>
      "Segera pesan melalui GENCO untuk memperoleh estimasi cashback sebesar Rp${amount}.";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about": MessageLookupByLibrary.simpleMessage("Tentang"),
    "account_empty_hint": MessageLookupByLibrary.simpleMessage(
      "Nomor WhatsApp tidak boleh kosong",
    ),
    "activity_rule": MessageLookupByLibrary.simpleMessage("Aturan Aktivitas"),
    "add_to_collection_success": MessageLookupByLibrary.simpleMessage(
      "Ditambahkan ke Favorit",
    ),
    "agent": MessageLookupByLibrary.simpleMessage("Agen"),
    "agent_fee": MessageLookupByLibrary.simpleMessage("Biaya Agen"),
    "agree_with_payment_term": MessageLookupByLibrary.simpleMessage(
      "Apakah Anda setuju dengan syarat pembayaran?",
    ),
    "all": MessageLookupByLibrary.simpleMessage("Semua"),
    "and": MessageLookupByLibrary.simpleMessage("dan"),
    "back": MessageLookupByLibrary.simpleMessage("Kembali"),
    "bank_card_number": MessageLookupByLibrary.simpleMessage("Nomor Rekening"),
    "bank_name": MessageLookupByLibrary.simpleMessage("Nama Bank"),
    "become_member": MessageLookupByLibrary.simpleMessage("Menjadi Agen"),
    "benefit": MessageLookupByLibrary.simpleMessage("Manfaat"),
    "bind_bank_card_confirm": MessageLookupByLibrary.simpleMessage(
      "Konfirmasi Kartu",
    ),
    "bonus": MessageLookupByLibrary.simpleMessage("Hadiah"),
    "brand_filter_all": MessageLookupByLibrary.simpleMessage("Semua"),
    "brand_filter_latest": MessageLookupByLibrary.simpleMessage("Baru"),
    "brand_filter_price": MessageLookupByLibrary.simpleMessage("Harga"),
    "brand_filter_rebate_rate": MessageLookupByLibrary.simpleMessage(
      "Cashback",
    ),
    "brand_filter_sales": MessageLookupByLibrary.simpleMessage("Penjualan"),
    "brand_high_rebate_title": MessageLookupByLibrary.simpleMessage(
      "Brand Cashback Tinggi",
    ),
    "brand_highest_rebate_rate": MessageLookupByLibrary.simpleMessage(
      "Cashback Tertinggi",
    ),
    "brand_home_top_subtitle": MessageLookupByLibrary.simpleMessage(
      "Temukan favoritmu dengan harga terbaik & cashback!",
    ),
    "brand_home_top_title": MessageLookupByLibrary.simpleMessage(
      "Brand Pilihan <red>Premium</red>",
    ),
    "brand_tiktok_hot_sale_title": MessageLookupByLibrary.simpleMessage(
      "Brand Populer di TikTok",
    ),
    "button_next": MessageLookupByLibrary.simpleMessage("Lanjut"),
    "can_not_open_link": MessageLookupByLibrary.simpleMessage(
      "Tautan Tidak Bisa Dibuka",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Batal"),
    "cancel_select_all": MessageLookupByLibrary.simpleMessage(
      "Batalkan Pilihan",
    ),
    "cashback_is_0": MessageLookupByLibrary.simpleMessage(
      "Produk ini tidak ada cashback",
    ),
    "cashback_is_0_content": MessageLookupByLibrary.simpleMessage(
      "Produk ini tidak ada cashback. Apakah Anda ingin melanjutkan?",
    ),
    "check_cash_back": MessageLookupByLibrary.simpleMessage("Cek Cashback"),
    "check_payment_result": MessageLookupByLibrary.simpleMessage(
      "Memeriksa Hasil Pembayaran",
    ),
    "collection": MessageLookupByLibrary.simpleMessage("Favorit"),
    "confirm": MessageLookupByLibrary.simpleMessage("OK"),
    "congratulation_to_add_group": MessageLookupByLibrary.simpleMessage(
      "Selamat Anda bergabung dengan",
    ),
    "contact_up": MessageLookupByLibrary.simpleMessage("Hubungi Atasan"),
    "copy_success": MessageLookupByLibrary.simpleMessage("Berhasil Disalin"),
    "credited_rebase_income": MessageLookupByLibrary.simpleMessage(
      "Cashback Diterima",
    ),
    "cumulative_number_of_invitations": MessageLookupByLibrary.simpleMessage(
      "Jumlah Undangan Kumulatif",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("Hapus"),
    "delete_account": MessageLookupByLibrary.simpleMessage("Nonaktifkan Akun"),
    "delete_account_content": MessageLookupByLibrary.simpleMessage(
      "Setelah akun dihapus, semua hak dan manfaat terkait Anda akan hilang secara permanen,\ndan tidak akan bisa masuk lagi ke akun ini.",
    ),
    "delete_account_title": MessageLookupByLibrary.simpleMessage(
      "Konfirmasi Penghapusan Akun?",
    ),
    "detail_brand_product_amount": MessageLookupByLibrary.simpleMessage(
      "produk",
    ),
    "detail_brand_product_amount_pre": MessageLookupByLibrary.simpleMessage(
      "Total",
    ),
    "detail_cashback_amount": MessageLookupByLibrary.simpleMessage(
      "Perkiraan Cashback",
    ),
    "detail_cashback_flow_check": MessageLookupByLibrary.simpleMessage(
      "Lihat Panduan",
    ),
    "detail_cashback_flow_step1": MessageLookupByLibrary.simpleMessage(
      "Klik produk",
    ),
    "detail_cashback_flow_step2": MessageLookupByLibrary.simpleMessage(
      "Pesan via\nTikTok",
    ),
    "detail_cashback_flow_step3": MessageLookupByLibrary.simpleMessage(
      "Lacak Pesanan",
    ),
    "detail_cashback_flow_step4": MessageLookupByLibrary.simpleMessage(
      "Dapatkan\nCashback",
    ),
    "detail_cashback_flow_title": MessageLookupByLibrary.simpleMessage(
      "Alur Dapatkan Cashback",
    ),
    "detail_price_title": MessageLookupByLibrary.simpleMessage("Harga"),
    "detail_rebate_rate": MessageLookupByLibrary.simpleMessage(
      "Persentase Cashback",
    ),
    "detail_sold": MessageLookupByLibrary.simpleMessage("Terjual"),
    "detail_sold_count": MessageLookupByLibrary.simpleMessage("pcs"),
    "diamond": MessageLookupByLibrary.simpleMessage("Berlian"),
    "diamond_agent": MessageLookupByLibrary.simpleMessage("Agen Berlian"),
    "diamond_partner": MessageLookupByLibrary.simpleMessage("Mitra Berlian"),
    "direct_invite_detail": MessageLookupByLibrary.simpleMessage(
      "Undang 1 agen dan dapatkan hadiah Rp 35.000. Undang 3 orang untuk balik modal!",
    ),
    "direct_invite_detail2": MessageLookupByLibrary.simpleMessage(
      "Tiap undang 1 agen, hadiah Rp 200.000, undang 3 orang balik modal seketika!",
    ),
    "direct_invite_detail3": MessageLookupByLibrary.simpleMessage(
      "Undang agen, hadiah Rp 35.000 / orang",
    ),
    "direct_invite_reward": MessageLookupByLibrary.simpleMessage(
      "Hadiah Undangan Langsung",
    ),
    "e_wallet": MessageLookupByLibrary.simpleMessage("E-Wallet"),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "error_button_title": MessageLookupByLibrary.simpleMessage("Coba Lagi"),
    "error_title": MessageLookupByLibrary.simpleMessage(
      "Maaf! Terjadi kesalahan. Silakan coba lagi.",
    ),
    "exclusive_benefits": MessageLookupByLibrary.simpleMessage(
      "Manfaat Eksklusif",
    ),
    "expenditure": MessageLookupByLibrary.simpleMessage("Pengeluaran"),
    "extra_bonus": MessageLookupByLibrary.simpleMessage("Bonus Ekstra"),
    "extra_cashback": MessageLookupByLibrary.simpleMessage("Cashback Ekstra"),
    "extra_cashback_detail": MessageLookupByLibrary.simpleMessage(
      "Nikmati manfaat cashback ekstra selama periode tertentu",
    ),
    "extra_cashback_detail_gold": MessageLookupByLibrary.simpleMessage(
      "Tiap kembangkan 10 Agen Emas: Hadiah Rp 300.000",
    ),
    "feedback_cash": MessageLookupByLibrary.simpleMessage("Feedback Cashback"),
    "find_product": MessageLookupByLibrary.simpleMessage("Temukan Produk"),
    "finish": MessageLookupByLibrary.simpleMessage("Selesai"),
    "gold": MessageLookupByLibrary.simpleMessage("Emas"),
    "gold_agent": MessageLookupByLibrary.simpleMessage("Agen Emas"),
    "gold_partner": MessageLookupByLibrary.simpleMessage("Mitra Emas"),
    "group": MessageLookupByLibrary.simpleMessage(" tim"),
    "guide_step1_content_flow_1": MessageLookupByLibrary.simpleMessage(
      "Buka Shopee/TikTok, salin tautan produk",
    ),
    "guide_step1_content_flow_1_description":
        MessageLookupByLibrary.simpleMessage(
          "Di Shopee/TikTok: Klik \'Bagikan\' > \'Salin Tautan\'.",
        ),
    "guide_step1_content_flow_1_title": MessageLookupByLibrary.simpleMessage(
      "SALIN TAUTAN PRODUK",
    ),
    "guide_step1_content_flow_2": MessageLookupByLibrary.simpleMessage(
      "Buka GENCO, cek cashback",
    ),
    "guide_step1_content_flow_2_description":
        MessageLookupByLibrary.simpleMessage(
          "Buka GENCO > Tempel otomatis atau ketik manual.",
        ),
    "guide_step1_content_flow_2_title": MessageLookupByLibrary.simpleMessage(
      "CEK CASHBACK DI GENCO",
    ),
    "guide_step1_content_flow_3": MessageLookupByLibrary.simpleMessage(
      "Klik ke Shopee/TikTok, beli",
    ),
    "guide_step1_content_flow_3_description":
        MessageLookupByLibrary.simpleMessage(
          "Klik \'Beli di Shopee/TikTok\' > Checkout seperti biasa.",
        ),
    "guide_step1_content_flow_3_title": MessageLookupByLibrary.simpleMessage(
      "BELI VIA GENCO",
    ),
    "guide_step1_content_flow_4": MessageLookupByLibrary.simpleMessage(
      "Cashback masuk setelah selesai",
    ),
    "guide_step1_content_flow_4_description": MessageLookupByLibrary.simpleMessage(
      "Cashback otomatis masuk setelah klik \'Pesanan Selesai\' di Shopee/TikTok.",
    ),
    "guide_step1_content_flow_4_title": MessageLookupByLibrary.simpleMessage(
      "DAPATKAN CASHBACK",
    ),
    "guide_step1_content_flow_5_description":
        MessageLookupByLibrary.simpleMessage(
          "Tonton video cara klaim cashback!",
        ),
    "guide_step1_content_flow_5_title": MessageLookupByLibrary.simpleMessage(
      "Panduan Video",
    ),
    "guide_step1_title": MessageLookupByLibrary.simpleMessage(
      "4 Langkah Dapat Cashback",
    ),
    "high_cashback": MessageLookupByLibrary.simpleMessage("Cashback Tinggi"),
    "high_cashback_description": MessageLookupByLibrary.simpleMessage(
      "Semakin banyak belanja, semakin banyak hemat",
    ),
    "home_cashback_button_title": MessageLookupByLibrary.simpleMessage(
      "Perkiraan jumlah cashback",
    ),
    "home_cashback_instructions_check_all":
        MessageLookupByLibrary.simpleMessage("Lihat Semua"),
    "home_cashback_instructions_step1": MessageLookupByLibrary.simpleMessage(
      "Salin\ntautan produk",
    ),
    "home_cashback_instructions_step2": MessageLookupByLibrary.simpleMessage(
      "Buka GENCO\nCek cashback",
    ),
    "home_cashback_instructions_step3": MessageLookupByLibrary.simpleMessage(
      "Beli di\nShopee/TikTok",
    ),
    "home_cashback_instructions_step4": MessageLookupByLibrary.simpleMessage(
      "Dapatkan Cashback",
    ),
    "home_cashback_instructions_title": MessageLookupByLibrary.simpleMessage(
      "Cara Dapatkan Cashback",
    ),
    "home_logo_slogan": MessageLookupByLibrary.simpleMessage(
      "Cari & Bandingkan, Dapat Cashback!",
    ),
    "home_navigation_brand": MessageLookupByLibrary.simpleMessage("Brand"),
    "home_navigation_home": MessageLookupByLibrary.simpleMessage("Beranda"),
    "home_navigation_income": MessageLookupByLibrary.simpleMessage(
      "Pendapatan",
    ),
    "home_navigation_mine": MessageLookupByLibrary.simpleMessage("Saya"),
    "home_platform_all": MessageLookupByLibrary.simpleMessage("Semua"),
    "home_platform_high_rebate": MessageLookupByLibrary.simpleMessage(
      "Cashback Tinggi",
    ),
    "home_platform_hot_sale": MessageLookupByLibrary.simpleMessage("Terlaris"),
    "home_platform_shopee": MessageLookupByLibrary.simpleMessage("Shopee"),
    "home_platform_tiktok": MessageLookupByLibrary.simpleMessage("TikTok"),
    "home_rebate_rate_title": MessageLookupByLibrary.simpleMessage(
      "Persentase Cashback",
    ),
    "home_search_button_title": MessageLookupByLibrary.simpleMessage("Tempel"),
    "home_search_instructions_copy": MessageLookupByLibrary.simpleMessage(
      "Salin",
    ),
    "home_search_instructions_text": MessageLookupByLibrary.simpleMessage(
      "Buka tautan di GENCO, raih cashback",
    ),
    "home_search_placeholder": MessageLookupByLibrary.simpleMessage(
      "Salin tautan produk, dapatkan cashback",
    ),
    "income": MessageLookupByLibrary.simpleMessage("Pemasukan"),
    "income_actual_credited_amount": MessageLookupByLibrary.simpleMessage(
      "Jumlah Cashback Sebenarnya",
    ),
    "income_actual_credited_amount_hint": MessageLookupByLibrary.simpleMessage(
      "Sudah Masuk ke Akun",
    ),
    "income_amount_available_for_withdrawal":
        MessageLookupByLibrary.simpleMessage("Saldo yang dapat ditarik"),
    "income_amount_credited": MessageLookupByLibrary.simpleMessage(
      "Dana Masuk",
    ),
    "income_amount_credited_description": MessageLookupByLibrary.simpleMessage(
      "Jumlah yang sudah diterima",
    ),
    "income_amount_to_be_credited": MessageLookupByLibrary.simpleMessage(
      "Menunggu Pembayaran",
    ),
    "income_amount_to_be_credited_hint": MessageLookupByLibrary.simpleMessage(
      "Termasuk cashback pesanan yang belum dikonfirmasi. Hanya perkiraan.",
    ),
    "income_campaign_reward": MessageLookupByLibrary.simpleMessage(
      "Hadiah Event",
    ),
    "income_expected_total_amount": MessageLookupByLibrary.simpleMessage(
      "Total Estimasi Cashback",
    ),
    "income_expected_total_amount_hint": MessageLookupByLibrary.simpleMessage(
      "Estimasi cashback pesanan dibayar. Dapat berubah jika ada refund.",
    ),
    "income_income_detail": MessageLookupByLibrary.simpleMessage(
      "Detail Pendapatan",
    ),
    "income_my_order": MessageLookupByLibrary.simpleMessage("Pesanan Saya"),
    "income_order_rebate": MessageLookupByLibrary.simpleMessage(
      "Cashback Pesanan",
    ),
    "income_pre_total_income": MessageLookupByLibrary.simpleMessage(
      "Pendapatan Diterima",
    ),
    "income_pre_total_income_description": MessageLookupByLibrary.simpleMessage(
      "Perkiraan total pendapatan sebagai referensi. Jumlah akhir akan disesuaikan dengan pembayaran aktual",
    ),
    "income_today": MessageLookupByLibrary.simpleMessage("Pendapatan Hari Ini"),
    "income_transaction_detail": MessageLookupByLibrary.simpleMessage(
      "Detail Transaksi",
    ),
    "income_transaction_history": MessageLookupByLibrary.simpleMessage(
      "Riwayat Transaksi",
    ),
    "income_transaction_history_empty": MessageLookupByLibrary.simpleMessage(
      "Belum ada transaksi",
    ),
    "income_withdrawal_amount": MessageLookupByLibrary.simpleMessage(
      "Jumlah Penarikan",
    ),
    "income_withdrawal_amount_hint": MessageLookupByLibrary.simpleMessage(
      "Masukkan jumlah penarikan",
    ),
    "income_withdrawal_button": MessageLookupByLibrary.simpleMessage(
      "Tarik Dana",
    ),
    "income_withdrawal_failed": MessageLookupByLibrary.simpleMessage(
      "Penarikan Gagal",
    ),
    "income_withdrawal_success": MessageLookupByLibrary.simpleMessage(
      "Penarikan Berhasil!",
    ),
    "input_bank_card_number": MessageLookupByLibrary.simpleMessage(
      "Masukkan nomor rekening",
    ),
    "input_invite_code": MessageLookupByLibrary.simpleMessage(
      "Masukkan Kode Undangan",
    ),
    "input_opt_verification_code": MessageLookupByLibrary.simpleMessage(
      "Masukkan Kode OTP",
    ),
    "input_opt_verification_code_error": MessageLookupByLibrary.simpleMessage(
      "Harap masukkan kode OTP",
    ),
    "input_opt_verification_code_hint": m0,
    "input_password_hint": MessageLookupByLibrary.simpleMessage(
      "Kata sandi Anda",
    ),
    "invite_agent": MessageLookupByLibrary.simpleMessage("Undang Agen"),
    "invite_and_earn_money": MessageLookupByLibrary.simpleMessage(
      "Undang dan Hasilkan Uang",
    ),
    "invite_and_eran_bonus": MessageLookupByLibrary.simpleMessage(
      "Undang dan Dapatkan Bonus",
    ),
    "invite_bonus": MessageLookupByLibrary.simpleMessage("Bonus Undangan"),
    "invite_code": MessageLookupByLibrary.simpleMessage("Kode Undangan"),
    "invite_code_empty_hint": MessageLookupByLibrary.simpleMessage(
      "Kode undangan tidak boleh kosong!",
    ),
    "invite_diamond_agent": MessageLookupByLibrary.simpleMessage(
      "Undang Agen Berlian",
    ),
    "invite_gold_agent": MessageLookupByLibrary.simpleMessage(
      "Undang Agen Emas",
    ),
    "invite_normal_user": MessageLookupByLibrary.simpleMessage(
      "Undang Pengguna Biasa",
    ),
    "invite_time": MessageLookupByLibrary.simpleMessage("Waktu Undangan"),
    "invite_to_upgrade": MessageLookupByLibrary.simpleMessage(
      "Undang 10 teman untuk menjadi Agen Perak atau lebih tinggi",
    ),
    "invite_to_upgrade_empty": MessageLookupByLibrary.simpleMessage(
      "Undang 10 teman baru untuk bergabung menjadi Agen Perak atau level lebih tinggi.\\nAnda akan ter-upgrade status secara otomatis!",
    ),
    "jump_link_failed": MessageLookupByLibrary.simpleMessage(
      "Gagal Membuka Tautan",
    ),
    "jump_to_tiktok": MessageLookupByLibrary.simpleMessage(
      "Segera dialihkan ke TikTok",
    ),
    "level_status": MessageLookupByLibrary.simpleMessage("Status Level"),
    "level_up_bonus": MessageLookupByLibrary.simpleMessage(
      "Progres dan Hadiah Naik Level",
    ),
    "level_up_content_title": MessageLookupByLibrary.simpleMessage(
      "Undang 10 teman menjadi Agen Perak atau level lebih tinggi",
    ),
    "level_up_description": MessageLookupByLibrary.simpleMessage(
      "Penjelasan Naik Level",
    ),
    "level_up_description_title": MessageLookupByLibrary.simpleMessage(
      "IDR Di Tangan",
    ),
    "level_up_description_title1": MessageLookupByLibrary.simpleMessage(
      "Cukup kembangkan 10 Agen Berlian!",
    ),
    "level_up_schedule": MessageLookupByLibrary.simpleMessage(
      "Jadwal Naik Level",
    ),
    "loading_more_empty": MessageLookupByLibrary.simpleMessage(
      "Belum ada wallpaper ~",
    ),
    "loading_more_error": MessageLookupByLibrary.simpleMessage(
      "Gagal memuat, silakan coba lagi",
    ),
    "loading_more_no_more": MessageLookupByLibrary.simpleMessage(
      "Sudah sampai akhir~",
    ),
    "loading_more_retry": MessageLookupByLibrary.simpleMessage("Coba Lagi"),
    "loading_more_write": MessageLookupByLibrary.simpleMessage("Tulis Ulasan"),
    "login": MessageLookupByLibrary.simpleMessage("Masuk"),
    "login_agreement": MessageLookupByLibrary.simpleMessage(
      "Dengan masuk, Anda menyetujui",
    ),
    "login_button": MessageLookupByLibrary.simpleMessage("Masuk"),
    "login_code_hint": MessageLookupByLibrary.simpleMessage(
      "Masukkan kode verifikasi",
    ),
    "login_code_label": MessageLookupByLibrary.simpleMessage("Kode Verifikasi"),
    "login_code_seconds": MessageLookupByLibrary.simpleMessage("detik"),
    "login_code_sent": MessageLookupByLibrary.simpleMessage(
      "Kode verifikasi telah dikirim",
    ),
    "login_enter_phone": MessageLookupByLibrary.simpleMessage(
      "Harap masukkan nomor ponsel",
    ),
    "login_enter_phone_code": MessageLookupByLibrary.simpleMessage(
      "Masukkan nomor ponsel dan kode",
    ),
    "login_expired_hint": MessageLookupByLibrary.simpleMessage(
      "Login telah kedaluwarsa, silakan login kembali!",
    ),
    "login_failed": MessageLookupByLibrary.simpleMessage("Gagal masuk"),
    "login_get_code": MessageLookupByLibrary.simpleMessage("Dapatkan Kode"),
    "login_mobile_subtitle": MessageLookupByLibrary.simpleMessage(
      "Silakan masuk dengan nomor ponsel",
    ),
    "login_mobile_title": MessageLookupByLibrary.simpleMessage(
      "Masuk via Nomor Ponsel",
    ),
    "login_password": MessageLookupByLibrary.simpleMessage("Kata Sandi"),
    "login_password_alternative": MessageLookupByLibrary.simpleMessage(
      "Masuk dengan Sandi",
    ),
    "login_password_confirm": MessageLookupByLibrary.simpleMessage(
      "Konfirmasi Sandi",
    ),
    "login_password_confirm_hint": MessageLookupByLibrary.simpleMessage(
      "Masukkan kembali sandi",
    ),
    "login_phone_hint": MessageLookupByLibrary.simpleMessage(
      "Masukkan nomor ponsel",
    ),
    "login_phone_label": MessageLookupByLibrary.simpleMessage("Nomor Ponsel"),
    "login_send_failed": MessageLookupByLibrary.simpleMessage(
      "Pengiriman kode gagal",
    ),
    "login_subtitle": MessageLookupByLibrary.simpleMessage(
      "Beli Hemat,\nBagikan Dapat Duit",
    ),
    "login_success": MessageLookupByLibrary.simpleMessage("Berhasil Masuk"),
    "login_title": MessageLookupByLibrary.simpleMessage(
      "Selamat Datang di GENCO",
    ),
    "login_welcome_back": MessageLookupByLibrary.simpleMessage(
      "Selamat Datang Kembali",
    ),
    "login_with_other_method": MessageLookupByLibrary.simpleMessage(
      "Masuk dengan metode lain",
    ),
    "login_with_password": MessageLookupByLibrary.simpleMessage(
      "Masuk dengan Sandi",
    ),
    "login_with_tiktok": MessageLookupByLibrary.simpleMessage(
      "Masuk dengan TikTok",
    ),
    "login_with_verification_code": MessageLookupByLibrary.simpleMessage(
      "Masuk dengan Kode OTP",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Keluar"),
    "logout_confirm_message": MessageLookupByLibrary.simpleMessage(
      "Yakin ingin keluar dari akun?",
    ),
    "logout_confirm_title": MessageLookupByLibrary.simpleMessage(
      "Konfirmasi Keluar",
    ),
    "member_benefits_partner_agent_1": MessageLookupByLibrary.simpleMessage(
      "Masa Berlaku",
    ),
    "member_benefits_partner_agent_1_value":
        MessageLookupByLibrary.simpleMessage("Permanen"),
    "member_benefits_partner_agent_2": MessageLookupByLibrary.simpleMessage(
      "Bonus Undangan",
    ),
    "member_benefits_partner_agent_2_value":
        MessageLookupByLibrary.simpleMessage(
          "Dapatkan bonus tinggi 10 miliar+ jika berhasil",
        ),
    "member_benefits_partner_agent_3": MessageLookupByLibrary.simpleMessage(
      "Komisi Belanja Tim",
    ),
    "member_benefits_partner_agent_3_value":
        MessageLookupByLibrary.simpleMessage(
          "Dapatkan hingga 20% cashback dari setiap cashback jaringan bawah",
        ),
    "member_benefits_partner_agent_4": MessageLookupByLibrary.simpleMessage(
      "Cashback Tambahan",
    ),
    "member_benefits_partner_agent_4_value":
        MessageLookupByLibrary.simpleMessage(
          "Cashback lebih tinggi, hingga 100%",
        ),
    "member_benefits_silver_agent_1": MessageLookupByLibrary.simpleMessage(
      "Masa Berlaku",
    ),
    "member_benefits_silver_agent_1_value":
        MessageLookupByLibrary.simpleMessage("1 Tahun"),
    "member_benefits_silver_agent_2": MessageLookupByLibrary.simpleMessage(
      "Bonus Undangan",
    ),
    "member_benefits_silver_agent_2_benefit":
        MessageLookupByLibrary.simpleMessage(
          "Bisa dapatkan bonus hingga Rp 10.000.000",
        ),
    "member_benefits_silver_agent_2_value":
        MessageLookupByLibrary.simpleMessage(
          "Undang berhasil, dapatkan hadiah tambahan",
        ),
    "member_benefits_silver_agent_3": MessageLookupByLibrary.simpleMessage(
      "Komisi Belanja Tim",
    ),
    "member_benefits_silver_agent_3_benefit": MessageLookupByLibrary.simpleMessage(
      "Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) ",
    ),
    "member_benefits_silver_agent_3_value":
        MessageLookupByLibrary.simpleMessage(
          "Belanja tim, Anda dapatkan keuntungan komisi",
        ),
    "member_benefits_silver_agent_4": MessageLookupByLibrary.simpleMessage(
      "Cashback Tambahan",
    ),
    "member_benefits_silver_agent_4_benefit": MessageLookupByLibrary.simpleMessage(
      "Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) ",
    ),
    "member_benefits_silver_agent_4_value":
        MessageLookupByLibrary.simpleMessage(
          "Cashback lebih tinggi, hingga 50%",
        ),
    "member_benefits_silver_agent_5": MessageLookupByLibrary.simpleMessage(
      "Cashback Tanpa Batas",
    ),
    "member_benefits_silver_agent_5_benefit": MessageLookupByLibrary.simpleMessage(
      "Komisi 10% dari cashback belanja undangan langsung (5% komisi reguler + 5% subsidi aktivitas) ",
    ),
    "member_benefits_silver_agent_5_value":
        MessageLookupByLibrary.simpleMessage("Cashback tanpa batas"),
    "member_introduction": MessageLookupByLibrary.simpleMessage(
      "Tingkatkan menjadi agen atau mitra untuk menghasilkan lebih banyak",
    ),
    "member_introduction_level_silver_agent":
        MessageLookupByLibrary.simpleMessage("Silver"),
    "member_level_partner": MessageLookupByLibrary.simpleMessage("Mitra"),
    "member_level_partner_fee": MessageLookupByLibrary.simpleMessage(
      "Biaya Mitra",
    ),
    "member_level_silver_agent": MessageLookupByLibrary.simpleMessage(
      "Agen Perak",
    ),
    "member_level_silver_agent_fee": MessageLookupByLibrary.simpleMessage(
      "Biaya Agen Perak",
    ),
    "member_level_state": MessageLookupByLibrary.simpleMessage("Status Level"),
    "member_status_description": MessageLookupByLibrary.simpleMessage(
      "Anda saat ini belum menjadi agen atau mitra",
    ),
    "message_no_data": MessageLookupByLibrary.simpleMessage("Belum ada data"),
    "modify_nickname": MessageLookupByLibrary.simpleMessage("Ubah Nickname"),
    "modify_password": MessageLookupByLibrary.simpleMessage("Ubah Kata Sandi"),
    "modify_phone_number": MessageLookupByLibrary.simpleMessage(
      "Ubah Nomor Ponsel",
    ),
    "modify_success": MessageLookupByLibrary.simpleMessage("Berhasil Diubah"),
    "my_avatar": MessageLookupByLibrary.simpleMessage("Foto Profil"),
    "my_collection": MessageLookupByLibrary.simpleMessage("Favorit Saya"),
    "my_team": MessageLookupByLibrary.simpleMessage("Tim Saya"),
    "name": MessageLookupByLibrary.simpleMessage("Nama"),
    "name_placeholder": MessageLookupByLibrary.simpleMessage(
      "Nama sesuai rekening",
    ),
    "network_error": MessageLookupByLibrary.simpleMessage("Kesalahan jaringan"),
    "network_is_not_available": MessageLookupByLibrary.simpleMessage(
      "Jaringan tidak tersedia, silakan periksa koneksi Anda",
    ),
    "next_step": MessageLookupByLibrary.simpleMessage("Lanjut"),
    "nickname": MessageLookupByLibrary.simpleMessage("Nama Panggilan"),
    "nickname_hint": MessageLookupByLibrary.simpleMessage(
      "Silakan masukkan nama panggilan",
    ),
    "nickname_too_long": MessageLookupByLibrary.simpleMessage(
      "Nama panggilan terlalu panjang, maksimal 10 karakter",
    ),
    "no_limit": MessageLookupByLibrary.simpleMessage("Tanpa Batas"),
    "no_limit_description": MessageLookupByLibrary.simpleMessage(
      "Nikmati cashback tanpa batas",
    ),
    "normal_member": MessageLookupByLibrary.simpleMessage("Member Biasa"),
    "normal_member_user": MessageLookupByLibrary.simpleMessage("Normal"),
    "normal_user": MessageLookupByLibrary.simpleMessage("Pengguna Biasa"),
    "normal_user_2_benefit": MessageLookupByLibrary.simpleMessage(
      "Tidak ada bonus",
    ),
    "normal_user_3_benefit": MessageLookupByLibrary.simpleMessage(
      "Tidak ada komisi belanja",
    ),
    "normal_user_4_benefit": MessageLookupByLibrary.simpleMessage(
      "Tidak ada komisi belanja",
    ),
    "normal_user_5_benefit": MessageLookupByLibrary.simpleMessage(
      "Tidak ada komisi belanja",
    ),
    "ok": MessageLookupByLibrary.simpleMessage("OK"),
    "open_payment_link": MessageLookupByLibrary.simpleMessage(
      "Buka Tautan Pembayaran Langsung",
    ),
    "order_application_time": MessageLookupByLibrary.simpleMessage(
      "Waktu permintaan:",
    ),
    "order_cashback_info": MessageLookupByLibrary.simpleMessage(
      "Ketentuan Cashback",
    ),
    "order_expected_cashback": MessageLookupByLibrary.simpleMessage(
      "Estimasi cashback:",
    ),
    "order_payment": MessageLookupByLibrary.simpleMessage("Pembayaran Pesanan"),
    "order_price": MessageLookupByLibrary.simpleMessage("Jumlah Pesanan"),
    "order_right_now": MessageLookupByLibrary.simpleMessage("Beli Sekarang"),
    "order_status_completed": MessageLookupByLibrary.simpleMessage("Selesai"),
    "order_status_expired": MessageLookupByLibrary.simpleMessage("Kadaluarsa"),
    "order_status_processing": MessageLookupByLibrary.simpleMessage("Diproses"),
    "order_tab_all": MessageLookupByLibrary.simpleMessage("Semua"),
    "order_tab_completed": MessageLookupByLibrary.simpleMessage("Selesai"),
    "order_tab_expired": MessageLookupByLibrary.simpleMessage("Kadaluarsa"),
    "order_tab_processing": MessageLookupByLibrary.simpleMessage("Diproses"),
    "order_title": MessageLookupByLibrary.simpleMessage("Pesanan Saya"),
    "partner": MessageLookupByLibrary.simpleMessage("Mitra"),
    "partner_extra_bonus1": MessageLookupByLibrary.simpleMessage(
      "Tiap kembangkan 10 Mitra Perak: Hadiah Rp 1.000.000",
    ),
    "partner_extra_bonus2": MessageLookupByLibrary.simpleMessage(
      "Tiap kembangkan 10 Mitra Emas: Hadiah Rp 2.000.000",
    ),
    "partner_extra_bonus3": MessageLookupByLibrary.simpleMessage(
      "Tiap kembangkan 10 Mitra Emas: Hadiah Rp 2.000.000;\nTiap kembangkan 10 Mitra Berlian: Hadiah Rp 100.000.000.",
    ),
    "password_not_same": MessageLookupByLibrary.simpleMessage(
      "Sandi tidak cocok",
    ),
    "pay_with_qrcode": MessageLookupByLibrary.simpleMessage(
      "Bayar dengan QR Code",
    ),
    "pay_with_qrcode_usage": MessageLookupByLibrary.simpleMessage(
      "Scan kode QR untuk membuka tautan pembayaran dan melakukan pembayaran. Jika ingin membayar langsung di aplikasi ini, ketuk untuk membuka tautannya.",
    ),
    "payment_agreement": MessageLookupByLibrary.simpleMessage(
      "Baca dan Setujui",
    ),
    "payment_agreement_link": MessageLookupByLibrary.simpleMessage(
      "Syarat Pembayaran",
    ),
    "payment_amount": MessageLookupByLibrary.simpleMessage("Jumlah Pembayaran"),
    "payment_complete": MessageLookupByLibrary.simpleMessage(
      "Pembayaran Selesai",
    ),
    "payment_failed": MessageLookupByLibrary.simpleMessage("Pembayaran Gagal"),
    "payment_id": MessageLookupByLibrary.simpleMessage(
      "Nomor Transaksi Pembayaran",
    ),
    "payment_method": MessageLookupByLibrary.simpleMessage("Metode Pembayaran"),
    "payment_problem": MessageLookupByLibrary.simpleMessage(
      "Masalah Pembayaran",
    ),
    "payment_success": MessageLookupByLibrary.simpleMessage(
      "Pembayaran Sukses",
    ),
    "phone_number": MessageLookupByLibrary.simpleMessage("Nomor Telepon"),
    "phone_number_placeholder": MessageLookupByLibrary.simpleMessage(
      "Masukkan Nomor Telepon",
    ),
    "please_choose_payment_method": MessageLookupByLibrary.simpleMessage(
      "Silakan pilih metode pembayaran",
    ),
    "please_input_amount": MessageLookupByLibrary.simpleMessage(
      "Masukkan jumlah penarikan",
    ),
    "please_input_bank_number": MessageLookupByLibrary.simpleMessage(
      "Masukkan nomor rekening",
    ),
    "please_input_e_wallet_account": MessageLookupByLibrary.simpleMessage(
      "Masukkan akun dompet digital",
    ),
    "please_input_your_password": MessageLookupByLibrary.simpleMessage(
      "Masukkan kata sandi Anda",
    ),
    "please_select_bank": MessageLookupByLibrary.simpleMessage("Pilih bank"),
    "please_select_bank_or_e_wallet": MessageLookupByLibrary.simpleMessage(
      "Pilih Bank/Dompet Digital",
    ),
    "please_select_e_wallet": MessageLookupByLibrary.simpleMessage(
      "Pilih dompet digital",
    ),
    "please_select_withdrawal_account": MessageLookupByLibrary.simpleMessage(
      "Pilih akun penarikan",
    ),
    "pre_team_cashback": MessageLookupByLibrary.simpleMessage(
      "Perkiraan Cashback Kontribusi Tim",
    ),
    "privacy": MessageLookupByLibrary.simpleMessage("Privasi"),
    "privacy_policy": MessageLookupByLibrary.simpleMessage("Kebijakan Privasi"),
    "product_link_empty": MessageLookupByLibrary.simpleMessage(
      "Tidak menemukan produk untuk link tersebut",
    ),
    "product_link_empty_content": MessageLookupByLibrary.simpleMessage(
      "Tidak ditemukan produk yang sesuai untuk tautan produk ini. Harap periksa apakah tautannya benar, atau coba produk lain.",
    ),
    "product_name": MessageLookupByLibrary.simpleMessage("Nama Produk"),
    "purchase_right_now": MessageLookupByLibrary.simpleMessage("Beli Sekarang"),
    "qrcode": MessageLookupByLibrary.simpleMessage("KODE QR"),
    "real_payment_price": MessageLookupByLibrary.simpleMessage(
      "Jumlah Pembayaran Aktual",
    ),
    "rebase_cash": MessageLookupByLibrary.simpleMessage(
      "Cashback yang Diperoleh",
    ),
    "rebase_expenditure": MessageLookupByLibrary.simpleMessage(
      "Cashback Keluar",
    ),
    "rebase_income": MessageLookupByLibrary.simpleMessage("Cashback Masuk"),
    "rebase_info": MessageLookupByLibrary.simpleMessage("Detail Cashback"),
    "rebase_price": MessageLookupByLibrary.simpleMessage("Harga Beli"),
    "rebase_rate": MessageLookupByLibrary.simpleMessage("Persentase Cashback"),
    "rebate_genco_last_app_content": MessageLookupByLibrary.simpleMessage(
      "Cashback tidak dapat dilacak jika membuka aplikasi lain setelah GENCO",
    ),
    "rebate_genco_last_app_title": MessageLookupByLibrary.simpleMessage(
      "GENCO HARUS App Terakhir",
    ),
    "rebate_how_to_get_title": MessageLookupByLibrary.simpleMessage(
      "Cara Dapatkan Cashback:",
    ),
    "rebate_how_to_order_content": MessageLookupByLibrary.simpleMessage(
      "Setiap produk HARUS diakses lewat GENCO sebelum checkout.",
    ),
    "rebate_how_to_order_title": MessageLookupByLibrary.simpleMessage(
      "CARA BELI",
    ),
    "rebate_instruction_content": MessageLookupByLibrary.simpleMessage(
      "Cashback masuk ke saldo GENCO setelah pesanan dikonfirmasi di Shopee/TikTok. Proses verifikasi: 1-3 hari.",
    ),
    "rebate_instruction_title": MessageLookupByLibrary.simpleMessage(
      "Syarat Cashback",
    ),
    "rebate_step_1_content": MessageLookupByLibrary.simpleMessage(
      "Pesan via Shopee/TikTok lewat link GENCO.",
    ),
    "rebate_step_1_title": MessageLookupByLibrary.simpleMessage("01"),
    "rebate_step_2_content": MessageLookupByLibrary.simpleMessage(
      "GENCO verifikasi otomatis (1-3 hari).",
    ),
    "rebate_step_2_title": MessageLookupByLibrary.simpleMessage("02"),
    "rebate_step_3_content": MessageLookupByLibrary.simpleMessage(
      "Cashback masuk setelah pesanan SELESAI.",
    ),
    "rebate_step_3_title": MessageLookupByLibrary.simpleMessage("03"),
    "rebate_unsupported_order_content": MessageLookupByLibrary.simpleMessage(
      "Pesanan di luar tautan GENCO tidak memenuhi syarat.",
    ),
    "rebate_unsupported_order_title": MessageLookupByLibrary.simpleMessage(
      "Pesanan Tidak Memenuhi Syarat",
    ),
    "received_bonus": MessageLookupByLibrary.simpleMessage(
      "Bonus yang Diterima",
    ),
    "resend_code": MessageLookupByLibrary.simpleMessage("Kirim Ulang"),
    "resend_in": MessageLookupByLibrary.simpleMessage("Kirim Ulang dalam"),
    "role": MessageLookupByLibrary.simpleMessage("Peran"),
    "search": MessageLookupByLibrary.simpleMessage("Cari"),
    "seconds": MessageLookupByLibrary.simpleMessage(" detik"),
    "select_all": MessageLookupByLibrary.simpleMessage("Pilih Semua"),
    "select_bank": MessageLookupByLibrary.simpleMessage("Pilih Bank"),
    "select_e_wallet": MessageLookupByLibrary.simpleMessage("Pilih E-Wallet"),
    "set_password_hint": MessageLookupByLibrary.simpleMessage(
      "Sandi: 6-20 karakter (huruf + angka)",
    ),
    "setting": MessageLookupByLibrary.simpleMessage("Pengaturan"),
    "setting_login_password": MessageLookupByLibrary.simpleMessage(
      "Atur Kata Sandi",
    ),
    "share_text": m1,
    "shopping_bonus": MessageLookupByLibrary.simpleMessage("Bonus Belanja"),
    "silver": MessageLookupByLibrary.simpleMessage("Perak"),
    "silver_agent": MessageLookupByLibrary.simpleMessage("Agen Perak"),
    "silver_partner": MessageLookupByLibrary.simpleMessage("Mitra Perak"),
    "task_cash_income": MessageLookupByLibrary.simpleMessage(
      "Pendapatan Tunai (Rp)",
    ),
    "task_center": MessageLookupByLibrary.simpleMessage("Pusat Tugas"),
    "task_center_title": MessageLookupByLibrary.simpleMessage("Pusat Tugas"),
    "task_close": MessageLookupByLibrary.simpleMessage("Tutup"),
    "task_conditions_met": MessageLookupByLibrary.simpleMessage(
      "Kondisi Terpenuhi",
    ),
    "task_conditions_not_met": MessageLookupByLibrary.simpleMessage(
      "Kondisi Tidak Terpenuhi",
    ),
    "task_daily_tasks": MessageLookupByLibrary.simpleMessage("Daftar Tugas"),
    "task_developing": MessageLookupByLibrary.simpleMessage(
      "Fitur Tugas Sedang Dikembangkan",
    ),
    "task_feature_developing": MessageLookupByLibrary.simpleMessage(
      "Fitur Sedang Dikembangkan",
    ),
    "task_go_claim": MessageLookupByLibrary.simpleMessage("Klaim"),
    "task_invite_count": MessageLookupByLibrary.simpleMessage(
      "Jumlah Undangan",
    ),
    "task_invite_progress": MessageLookupByLibrary.simpleMessage(
      "Progres Undangan",
    ),
    "task_invite_reward": MessageLookupByLibrary.simpleMessage(
      "Hadiah Pesanan Pertama Undangan",
    ),
    "task_order_count": MessageLookupByLibrary.simpleMessage("Jumlah Pesanan"),
    "task_order_progress": MessageLookupByLibrary.simpleMessage(
      "Progres Pesanan",
    ),
    "task_per_completion": MessageLookupByLibrary.simpleMessage(
      "Per Penyelesaian",
    ),
    "task_record_title": MessageLookupByLibrary.simpleMessage("Catatan Tugas"),
    "task_redeemed_invites": MessageLookupByLibrary.simpleMessage(
      "Undangan Ditukar",
    ),
    "task_redeemed_orders": MessageLookupByLibrary.simpleMessage(
      "Pesanan Ditukar",
    ),
    "task_return_cash_welfare": MessageLookupByLibrary.simpleMessage(
      "Kesejahteraan Uang Kembali",
    ),
    "task_return_cash_welfare_desc": MessageLookupByLibrary.simpleMessage(
      "Eksklusif Harian",
    ),
    "task_reward_amount": MessageLookupByLibrary.simpleMessage("Jumlah Hadiah"),
    "task_total_invites": MessageLookupByLibrary.simpleMessage(
      "Total Undangan",
    ),
    "task_total_orders": MessageLookupByLibrary.simpleMessage("Total Pesanan"),
    "task_view_record": MessageLookupByLibrary.simpleMessage("Lihat Catatan"),
    "task_withdraw": MessageLookupByLibrary.simpleMessage("Tarik"),
    "task_withdrawable_amount": MessageLookupByLibrary.simpleMessage(
      "Jumlah yang Dapat Ditarik",
    ),
    "team_bonus": MessageLookupByLibrary.simpleMessage("Bonus Tim"),
    "team_bonus_detail": MessageLookupByLibrary.simpleMessage(
      "Rekomendasi tidak langsung (tingkat dua): setiap orang terkumpul Rp 100.000;\nRekomendasi tidak langsung (tingkat tiga): setiap orang terkumpul Rp 50.000;\nJika Anda mencapai status “Mitra Emas” (berhasil merekomendasikan langsung 10 mitra), hadiah dapat ditarik, berlaku selama 60 hari sejak hadiah dihasilkan. Jika tidak mencapai dalam waktu tersebut, hadiah akan hangus secara otomatis.",
    ),
    "team_purchase_bonus": MessageLookupByLibrary.simpleMessage(
      "Bonus Belanja Tim",
    ),
    "team_purchase_detail": MessageLookupByLibrary.simpleMessage(
      "Dapatkan keuntungan 10% dari total belanja tim yang kamu undang langsung",
    ),
    "team_purchase_detail_gold": MessageLookupByLibrary.simpleMessage(
      "Undangan tidak langsung (tingkat dua): masing-masing dapat terkumpul Rp 15.000; Undangan tidak langsung (tingkat tiga): masing-masing dapat terkumpul Rp 10.000.",
    ),
    "team_purchase_detail_gold2": MessageLookupByLibrary.simpleMessage(
      "Undangan tidak langsung (tingkat dua): masing-masing dapat Rp 100.000; Undangan tidak langsung (tingkat tiga): masing-masing dapat Rp 50.000",
    ),
    "team_support": MessageLookupByLibrary.simpleMessage("Kontribusi Tim"),
    "to_gold_progress": MessageLookupByLibrary.simpleMessage(
      "Progres Menjadi Agen Emas",
    ),
    "today": MessageLookupByLibrary.simpleMessage("Hari Ini"),
    "trade_channel": MessageLookupByLibrary.simpleMessage("Channel"),
    "trade_order_number": MessageLookupByLibrary.simpleMessage("ID Pesanan"),
    "trade_serial_number": MessageLookupByLibrary.simpleMessage(
      "Nomor Transaksi",
    ),
    "trade_time": MessageLookupByLibrary.simpleMessage("Waktu"),
    "trade_type": MessageLookupByLibrary.simpleMessage("Jenis Transaksi"),
    "training": MessageLookupByLibrary.simpleMessage("Pelatihan"),
    "training_detail": MessageLookupByLibrary.simpleMessage(
      "Mentor profesional memberikan kursus berkualitas tinggi dan layanan bimbingan menyeluruh",
    ),
    "unknown_error": MessageLookupByLibrary.simpleMessage(
      "Kesalahan tidak diketahui",
    ),
    "upgrade_date": MessageLookupByLibrary.simpleMessage(
      "Tanggal Upgrade: 11 Juni 2025",
    ),
    "usage_guideline_description": MessageLookupByLibrary.simpleMessage(
      "Dapatkan cashback di Shopee/TikTok dalam 3 langkah mudah!",
    ),
    "usage_guideline_step1": MessageLookupByLibrary.simpleMessage(
      "Buka produk di <red>Shopee</red> atau <red>TikTok</red>, salin tautan produk",
    ),
    "usage_guideline_step2": MessageLookupByLibrary.simpleMessage(
      "Buka <red>GENCO</red>, tempel tautan, lihat cashback",
    ),
    "usage_guideline_step3": MessageLookupByLibrary.simpleMessage(
      "Klik ke <red>Shopee</red>/<red>TikTok</red> via GENCO, selesaikan pembelian",
    ),
    "usage_guideline_title": MessageLookupByLibrary.simpleMessage(
      "Panduan Pengguna",
    ),
    "usage_hint": MessageLookupByLibrary.simpleMessage("Hemat dengan GENCO!"),
    "user_agreement": MessageLookupByLibrary.simpleMessage("Syarat Penggunaan"),
    "user_service": MessageLookupByLibrary.simpleMessage("Layanan Pelanggan"),
    "user_service_description": MessageLookupByLibrary.simpleMessage(
      "Layanan pelanggan berkualitas",
    ),
    "valid_for": MessageLookupByLibrary.simpleMessage("Berlaku: 1 tahun"),
    "welcome_back": MessageLookupByLibrary.simpleMessage(
      "Hai, selamat datang kembali!",
    ),
    "whatsapp_account": MessageLookupByLibrary.simpleMessage("Akun WhatsApp"),
    "whatsapp_account_hint": MessageLookupByLibrary.simpleMessage(
      "Masukkan nomor WhatsApp Anda",
    ),
    "withdrawal_account": MessageLookupByLibrary.simpleMessage(
      "Akun Penarikan",
    ),
    "withdrawal_add_card": MessageLookupByLibrary.simpleMessage(
      "+ Rekening Bank",
    ),
    "withdrawal_add_e_card": MessageLookupByLibrary.simpleMessage(
      "+ Tambah E-Wallet",
    ),
    "withdrawal_all": MessageLookupByLibrary.simpleMessage("Tarik Semua"),
    "withdrawal_amount": MessageLookupByLibrary.simpleMessage(
      "Jumlah maksimum yang bisa ditarik",
    ),
    "withdrawal_amount_hint": MessageLookupByLibrary.simpleMessage(
      "Jumlah minimum yang bisa ditarik",
    ),
    "withdrawal_amount_min": MessageLookupByLibrary.simpleMessage(
      "Minimal penarikan",
    ),
    "withdrawal_choose_method": MessageLookupByLibrary.simpleMessage(
      "Pilih Akun Penarikan",
    ),
    "withdrawal_failed": MessageLookupByLibrary.simpleMessage(
      "Penarikan Gagal",
    ),
    "withdrawal_fees": MessageLookupByLibrary.simpleMessage("Biaya Admin"),
    "withdrawal_fees_hint": MessageLookupByLibrary.simpleMessage(
      "Biaya 1.5% dari penarikan. Minimal Rp5.550. Jika <Rp5.550, biaya tetap Rp5.550.",
    ),
    "withdrawal_finish": MessageLookupByLibrary.simpleMessage(
      "Penarikan Selesai",
    ),
    "withdrawal_hint": MessageLookupByLibrary.simpleMessage("Catatan"),
    "withdrawal_hint_description": MessageLookupByLibrary.simpleMessage(
      "Dana masuk ≤24 jam kerja (selain Sabtu/Minggu/libur)",
    ),
    "withdrawal_success": MessageLookupByLibrary.simpleMessage(
      "Penarikan berhasil diajukan!",
    ),
    "withdrawal_success_hint": MessageLookupByLibrary.simpleMessage(
      "Dana masuk ≤24 jam kerja. Cek saldo akun Anda!",
    ),
    "year": MessageLookupByLibrary.simpleMessage("Tahun"),
  };
}
